#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块1：源数据探索性分析
功能：对理财客户数据进行全面的探索性数据分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体，确保图表中文显示正常
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 指定中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class DataExplorer:
    """
    数据探索分析类
    用于对理财客户数据进行全面的探索性分析
    """
    
    def __init__(self, data_path, output_dir):
        """
        初始化数据探索器
        
        参数说明：
        data_path (str): 数据文件路径，指向宽表.csv文件
        output_dir (str): 输出目录路径，用于保存分析结果和图表
        """
        self.data_path = data_path  # 存储数据文件路径
        self.output_dir = output_dir  # 存储输出目录路径
        self.data = None  # 用于存储加载的数据
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # 生成时间戳用于文件命名
        
        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """
        加载数据文件
        处理UTF-8 BOM编码问题，确保数据正确读取
        
        返回值：
        bool: 数据加载是否成功
        """
        try:
            print("正在加载数据文件...")
            # 使用utf-8-sig编码处理BOM问题
            # encoding='utf-8-sig'会自动去除UTF-8 BOM标记
            self.data = pd.read_csv(self.data_path, encoding='utf-8-sig')
            print(f"数据加载成功！数据形状：{self.data.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def basic_info_analysis(self):
        """
        基本信息分析
        分析数据的基本统计信息、数据类型、缺失值等
        """
        print("\n" + "="*50)
        print("开始基本信息分析...")
        
        # 数据基本信息
        print(f"\n数据集基本信息：")
        print(f"- 数据形状：{self.data.shape[0]}行 × {self.data.shape[1]}列")
        print(f"- 内存使用：{self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        # 数据类型统计
        dtype_counts = self.data.dtypes.value_counts()
        print(f"\n数据类型分布：")
        for dtype, count in dtype_counts.items():
            print(f"- {dtype}: {count}列")
        
        # 缺失值分析
        missing_info = self.data.isnull().sum()
        missing_percent = (missing_info / len(self.data)) * 100
        missing_df = pd.DataFrame({
            '缺失数量': missing_info,
            '缺失比例(%)': missing_percent
        })
        # 只显示有缺失值的列
        missing_df = missing_df[missing_df['缺失数量'] > 0].sort_values('缺失比例(%)', ascending=False)
        
        if len(missing_df) > 0:
            print(f"\n缺失值统计（共{len(missing_df)}列有缺失值）：")
            print(missing_df.head(10))  # 显示缺失值最多的前10列
        else:
            print("\n数据集无缺失值")
        
        # 保存基本信息到文件
        info_file = os.path.join(self.output_dir, f"{self.timestamp}_basic_info.txt")
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write("理财客户数据基本信息分析报告\n")
            f.write("="*50 + "\n\n")
            f.write(f"数据形状：{self.data.shape[0]}行 × {self.data.shape[1]}列\n")
            f.write(f"内存使用：{self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\n\n")
            f.write("数据类型分布：\n")
            for dtype, count in dtype_counts.items():
                f.write(f"- {dtype}: {count}列\n")
            f.write("\n缺失值统计：\n")
            if len(missing_df) > 0:
                f.write(missing_df.to_string())
            else:
                f.write("数据集无缺失值")
        
        return missing_df
    
    def target_variable_analysis(self):
        """
        目标变量分析
        分析"是否购买理财"列的分布情况
        """
        print("\n" + "="*50)
        print("开始目标变量分析...")
        
        target_col = '是否购买理财'  # 目标变量列名
        
        if target_col not in self.data.columns:
            print(f"警告：未找到目标变量列'{target_col}'")
            return
        
        # 目标变量分布统计
        target_counts = self.data[target_col].value_counts()
        target_percent = self.data[target_col].value_counts(normalize=True) * 100
        
        print(f"\n目标变量'{target_col}'分布：")
        for value, count in target_counts.items():
            percent = target_percent[value]
            print(f"- {value}: {count}个 ({percent:.2f}%)")
        
        # 创建目标变量分布图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 柱状图
        target_counts.plot(kind='bar', ax=ax1, color=['skyblue', 'lightcoral'])
        ax1.set_title('目标变量分布 - 数量', fontsize=14, fontweight='bold')
        ax1.set_xlabel('是否购买理财', fontsize=12)
        ax1.set_ylabel('客户数量', fontsize=12)
        ax1.tick_params(axis='x', rotation=45)
        
        # 在柱状图上添加数值标签
        for i, v in enumerate(target_counts.values):
            ax1.text(i, v + max(target_counts.values) * 0.01, str(v), 
                    ha='center', va='bottom', fontweight='bold')
        
        # 饼图
        colors = ['skyblue', 'lightcoral']
        wedges, texts, autotexts = ax2.pie(target_counts.values, labels=target_counts.index, 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax2.set_title('目标变量分布 - 比例', fontsize=14, fontweight='bold')
        
        # 美化饼图文字
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        plt.tight_layout()
        
        # 保存图表
        target_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_target_distribution.png")
        plt.savefig(target_plot_file, dpi=300, bbox_inches='tight')
        # plt.show()
        # plt.close()
        
        return target_counts
    
    def numerical_features_analysis(self):
        """
        数值型特征分析
        分析数值型特征的分布、统计特性等
        """
        print("\n" + "="*50)
        print("开始数值型特征分析...")
        
        # 识别数值型列（排除目标变量）
        numerical_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()
        if '是否购买理财' in numerical_cols:
            numerical_cols.remove('是否购买理财')
        
        print(f"发现{len(numerical_cols)}个数值型特征")
        
        if len(numerical_cols) == 0:
            print("未发现数值型特征")
            return
        
        # 数值型特征基本统计
        numerical_stats = self.data[numerical_cols].describe()
        print("\n数值型特征基本统计：")
        print(numerical_stats)
        
        # 保存统计信息
        stats_file = os.path.join(self.output_dir, f"{self.timestamp}_numerical_stats.csv")
        numerical_stats.to_csv(stats_file, encoding='utf-8-sig')
        
        # 绘制数值型特征分布图（选择前12个特征避免图表过于拥挤）
        plot_cols = numerical_cols[:12] if len(numerical_cols) > 12 else numerical_cols
        
        if len(plot_cols) > 0:
            # 计算子图布局
            n_cols = 4
            n_rows = (len(plot_cols) + n_cols - 1) // n_cols
            
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
            if n_rows == 1:
                axes = axes.reshape(1, -1)
            
            for i, col in enumerate(plot_cols):
                row = i // n_cols
                col_idx = i % n_cols
                ax = axes[row, col_idx]
                
                # 绘制直方图
                self.data[col].hist(bins=30, ax=ax, alpha=0.7, color='skyblue', edgecolor='black')
                ax.set_title(f'{col}分布', fontsize=10, fontweight='bold')
                ax.set_xlabel(col, fontsize=9)
                ax.set_ylabel('频次', fontsize=9)
                ax.grid(True, alpha=0.3)
            
            # 隐藏多余的子图
            for i in range(len(plot_cols), n_rows * n_cols):
                row = i // n_cols
                col_idx = i % n_cols
                axes[row, col_idx].set_visible(False)
            
            plt.tight_layout()
            
            # 保存图表
            numerical_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_numerical_distributions.png")
            plt.savefig(numerical_plot_file, dpi=300, bbox_inches='tight')
            # plt.show()
            # plt.close()
        
        return numerical_cols, numerical_stats

    def categorical_features_analysis(self):
        """
        分类型特征分析
        分析分类型特征的分布、唯一值等
        """
        print("\n" + "="*50)
        print("开始分类型特征分析...")

        # 识别分类型列（排除目标变量）
        categorical_cols = self.data.select_dtypes(include=['object']).columns.tolist()
        if '是否购买理财' in categorical_cols:
            categorical_cols.remove('是否购买理财')

        print(f"发现{len(categorical_cols)}个分类型特征")

        if len(categorical_cols) == 0:
            print("未发现分类型特征")
            return

        # 分类型特征基本信息
        categorical_info = []
        for col in categorical_cols:
            unique_count = self.data[col].nunique()
            missing_count = self.data[col].isnull().sum()
            missing_percent = (missing_count / len(self.data)) * 100

            categorical_info.append({
                '特征名': col,
                '唯一值数量': unique_count,
                '缺失值数量': missing_count,
                '缺失值比例(%)': round(missing_percent, 2)
            })

        categorical_df = pd.DataFrame(categorical_info)
        print("\n分类型特征基本信息：")
        print(categorical_df)

        # 保存分类特征信息
        categorical_file = os.path.join(self.output_dir, f"{self.timestamp}_categorical_info.csv")
        categorical_df.to_csv(categorical_file, index=False, encoding='utf-8-sig')

        # 绘制分类特征分布图（选择唯一值较少的特征进行可视化）
        plot_cols = [col for col in categorical_cols if self.data[col].nunique() <= 20][:8]

        if len(plot_cols) > 0:
            # 计算子图布局
            n_cols = 2
            n_rows = (len(plot_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 6*n_rows))
            if n_rows == 1:
                axes = axes.reshape(1, -1)
            elif len(plot_cols) == 1:
                axes = [axes]

            for i, col in enumerate(plot_cols):
                if n_rows > 1:
                    row = i // n_cols
                    col_idx = i % n_cols
                    ax = axes[row, col_idx]
                else:
                    ax = axes[i] if len(plot_cols) > 1 else axes

                # 计算值计数
                value_counts = self.data[col].value_counts().head(10)  # 只显示前10个最频繁的值

                # 绘制柱状图
                value_counts.plot(kind='bar', ax=ax, color='lightgreen', alpha=0.7)
                ax.set_title(f'{col}分布（前10个值）', fontsize=12, fontweight='bold')
                ax.set_xlabel(col, fontsize=10)
                ax.set_ylabel('频次', fontsize=10)
                ax.tick_params(axis='x', rotation=45)
                ax.grid(True, alpha=0.3)

                # 在柱状图上添加数值标签
                for j, v in enumerate(value_counts.values):
                    ax.text(j, v + max(value_counts.values) * 0.01, str(v),
                           ha='center', va='bottom', fontsize=8)

            # 隐藏多余的子图
            if n_rows > 1:
                for i in range(len(plot_cols), n_rows * n_cols):
                    row = i // n_cols
                    col_idx = i % n_cols
                    axes[row, col_idx].set_visible(False)

            plt.tight_layout()

            # 保存图表
            categorical_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_categorical_distributions.png")
            plt.savefig(categorical_plot_file, dpi=300, bbox_inches='tight')
            # plt.show()
            # plt.close()

        return categorical_cols, categorical_df

    def correlation_analysis(self):
        """
        相关性分析
        分析数值型特征之间的相关性
        """
        print("\n" + "="*50)
        print("开始相关性分析...")

        # 获取数值型特征
        numerical_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()

        if len(numerical_cols) < 2:
            print("数值型特征少于2个，跳过相关性分析")
            return

        # 计算相关性矩阵
        correlation_matrix = self.data[numerical_cols].corr()

        # 绘制相关性热力图
        plt.figure(figsize=(20, 25))

        # 创建掩码，只显示下三角
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

        # 绘制热力图
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

        plt.title('数值型特征相关性热力图', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()

        # 保存图表
        correlation_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_correlation_heatmap.png")
        plt.savefig(correlation_plot_file, dpi=300, bbox_inches='tight')
        # plt.show()
        # plt.close()

        # 保存相关性矩阵
        correlation_file = os.path.join(self.output_dir, f"{self.timestamp}_correlation_matrix.csv")
        correlation_matrix.to_csv(correlation_file, encoding='utf-8-sig')

        # 找出高相关性特征对（绝对值大于0.7）
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.7:
                    high_corr_pairs.append({
                        '特征1': correlation_matrix.columns[i],
                        '特征2': correlation_matrix.columns[j],
                        '相关系数': round(corr_value, 3)
                    })

        if high_corr_pairs:
            high_corr_df = pd.DataFrame(high_corr_pairs)
            print(f"\n发现{len(high_corr_pairs)}对高相关性特征（|相关系数| > 0.7）：")
            print(high_corr_df)

            # 保存高相关性特征对
            high_corr_file = os.path.join(self.output_dir, f"{self.timestamp}_high_correlation_pairs.csv")
            high_corr_df.to_csv(high_corr_file, index=False, encoding='utf-8-sig')
        else:
            print("\n未发现高相关性特征对（|相关系数| > 0.7）")

        return correlation_matrix

    def generate_summary_report(self):
        """
        生成EDA总结报告
        将所有分析结果汇总到一个Markdown文档中
        """
        print("\n" + "="*50)
        print("生成EDA总结报告...")

        report_file = os.path.join(self.output_dir, f"{self.timestamp}_EDA_summary_report.md")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 理财客户数据探索性分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            # 数据概览
            f.write("## 1. 数据概览\n\n")
            f.write(f"- **数据规模：** {self.data.shape[0]:,}行 × {self.data.shape[1]}列\n")
            f.write(f"- **内存使用：** {self.data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\n")
            f.write(f"- **数据源：** 宽表.csv\n\n")

            # 数据类型分布
            dtype_counts = self.data.dtypes.value_counts()
            f.write("### 数据类型分布\n\n")
            for dtype, count in dtype_counts.items():
                f.write(f"- **{dtype}：** {count}列\n")
            f.write("\n")

            # 缺失值情况
            missing_info = self.data.isnull().sum()
            missing_cols = missing_info[missing_info > 0]
            f.write("### 缺失值情况\n\n")
            if len(missing_cols) > 0:
                f.write(f"共有 **{len(missing_cols)}** 列存在缺失值：\n\n")
                for col, missing_count in missing_cols.items():
                    missing_percent = (missing_count / len(self.data)) * 100
                    f.write(f"- **{col}：** {missing_count:,}个缺失值 ({missing_percent:.2f}%)\n")
                # if len(missing_cols) > 10:
                #     f.write(f"- ... 还有{len(missing_cols) - 10}列存在缺失值\n")
            else:
                f.write("✅ **数据集无缺失值**\n")
            f.write("\n")

            # 目标变量分析
            target_col = '是否购买理财'
            if target_col in self.data.columns:
                target_counts = self.data[target_col].value_counts()
                f.write("## 2. 目标变量分析\n\n")
                f.write(f"目标变量 **'{target_col}'** 的分布情况：\n\n")
                for value, count in target_counts.items():
                    percent = (count / len(self.data)) * 100
                    f.write(f"- **{value}：** {count:,}个样本 ({percent:.2f}%)\n")
                f.write("\n")

                # 样本不平衡情况分析
                if len(target_counts) == 2:
                    ratio = max(target_counts.values) / min(target_counts.values)
                    f.write(f"**样本不平衡比例：** {ratio:.1f}:1\n\n")

            # 特征分析
            numerical_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()
            if target_col in numerical_cols:
                numerical_cols.remove(target_col)

            categorical_cols = self.data.select_dtypes(include=['object']).columns.tolist()
            if target_col in categorical_cols:
                categorical_cols.remove(target_col)

            f.write("## 3. 特征分析\n\n")
            f.write(f"### 数值型特征 ({len(numerical_cols)}个)\n\n")
            if len(numerical_cols) > 0:
                f.write("主要数值型特征包括：\n")
                for i, col in enumerate(numerical_cols):  # 只列出前10个
                    f.write(f"{i+1}. {col}\n")
                # if len(numerical_cols) > 10:
                #     f.write(f"... 还有{len(numerical_cols) - 10}个数值型特征\n")
            else:
                f.write("未发现数值型特征\n")
            f.write("\n")

            f.write(f"### 分类型特征 ({len(categorical_cols)}个)\n\n")
            if len(categorical_cols) > 0:
                f.write("主要分类型特征包括：\n")
                for i, col in enumerate(categorical_cols):  # 只列出前10个
                    unique_count = self.data[col].nunique()
                    f.write(f"{i+1}. {col} (唯一值: {unique_count}个)\n")
                # if len(categorical_cols) > 10:
                #     f.write(f"... 还有{len(categorical_cols) - 10}个分类型特征\n")
            else:
                f.write("未发现分类型特征\n")
            f.write("\n")

            # 关键发现
            f.write("## 4. 关键发现\n\n")
            f.write("### 数据质量\n")
            if len(missing_cols) == 0:
                f.write("✅ 数据完整性良好，无缺失值\n")
            else:
                f.write(f"⚠️ 存在缺失值，需要在预处理阶段处理\n")

            f.write("\n### 样本分布\n")
            if target_col in self.data.columns:
                target_counts = self.data[target_col].value_counts()
                unknown_count = target_counts.get('未知', 0)
                labeled_count = len(self.data) - unknown_count
                f.write(f"- 已标注样本：{labeled_count:,}个\n")
                f.write(f"- 未标注样本：{unknown_count:,}个\n")
                if unknown_count > 0:
                    f.write("- 适合使用PU学习方法进行建模\n")

            f.write("\n### 建议\n")
            f.write("1. **数据预处理：** 处理缺失值和异常值\n")
            f.write("2. **特征工程：** 对分类变量进行编码\n")
            f.write("3. **模型选择：** 考虑使用PU学习处理未标注样本\n")
            f.write("4. **特征选择：** 基于业务理解和统计方法筛选重要特征\n\n")

            f.write("---\n\n")
            f.write("*本报告由数据探索分析模块自动生成*\n")

        print(f"EDA总结报告已保存至：{report_file}")
        return report_file

    def run_complete_analysis(self):
        """
        运行完整的探索性数据分析流程

        返回值：
        bool: 分析是否成功完成
        """
        try:
            print("开始执行完整的探索性数据分析...")
            print("="*60)

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 基本信息分析
            self.basic_info_analysis()

            # 3. 目标变量分析
            self.target_variable_analysis()

            # 4. 数值型特征分析
            self.numerical_features_analysis()

            # 5. 分类型特征分析
            self.categorical_features_analysis()

            # 6. 相关性分析
            self.correlation_analysis()

            # 7. 生成总结报告
            self.generate_summary_report()

            print("\n" + "="*60)
            print("✅ 探索性数据分析完成！")
            print(f"所有结果已保存至：{self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 分析过程中出现错误：{str(e)}")
            return False


def main():
    """
    主函数：执行数据探索分析

    使用说明：
    1. 确保数据文件路径正确
    2. 确保输出目录存在或可创建
    3. 运行脚本即可生成完整的EDA报告

    迁移到其他项目时需要修改的部分：
    - data_path: 修改为实际的数据文件路径
    - output_dir: 修改为期望的输出目录
    - 目标变量列名（如果不是'是否购买理财'）
    """

    # 配置参数（迁移时需要修改这些路径）
    data_path = "宽表.csv"  # 数据文件路径
    output_dir = "output4/01_EDA"  # 输出目录

    # 创建数据探索器实例
    explorer = DataExplorer(data_path, output_dir)

    # 执行完整分析
    success = explorer.run_complete_analysis()

    if success:
        print("\n🎉 数据探索分析成功完成！")
        print("📊 请查看生成的图表和报告文件")
    else:
        print("\n❌ 数据探索分析失败，请检查错误信息")


if __name__ == "__main__":
    main()
