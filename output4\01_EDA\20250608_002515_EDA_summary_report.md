# 理财客户数据探索性分析报告

**生成时间：** 2025年06月08日 00:25:25

---

## 1. 数据概览

- **数据规模：** 334,001行 × 49列
- **内存使用：** 284.59 MB
- **数据源：** 宽表.csv

### 数据类型分布

- **float64：** 27列
- **int64：** 14列
- **object：** 8列

### 缺失值情况

共有 **24** 列存在缺失值：

- **首个账户开户日：** 1个缺失值 (0.00%)
- **当年月均交易笔数：** 121,279个缺失值 (36.31%)
- **表内贷款余额年日均：** 307,453个缺失值 (92.05%)
- **融资年日均：** 307,453个缺失值 (92.05%)
- **大额存单年日均余额：** 333,569个缺失值 (99.87%)
- **结构性存款年日均余额：** 332,250个缺失值 (99.48%)
- **存款总额年日均：** 5,452个缺失值 (1.63%)
- **国际结算-本年交易量折美元：** 330,289个缺失值 (98.89%)
- **即期结售汇-本年交易量折美元：** 330,779个缺失值 (99.04%)
- **线上供应链-余额：** 326,439个缺失值 (97.74%)
- **线上供应链-本年发生额：** 329,129个缺失值 (98.54%)
- **国内保函-余额：** 332,595个缺失值 (99.58%)
- **银承贴现-余额：** 332,483个缺失值 (99.55%)
- **银承贴现-本年发生额：** 332,483个缺失值 (99.55%)
- **贷款本年发生额：** 314,011个缺失值 (94.01%)
- **贷款余额：** 314,011个缺失值 (94.01%)
- **近12月活期转出金额：** 141,700个缺失值 (42.43%)
- **近12月活期转出笔数：** 141,700个缺失值 (42.43%)
- **近12月活期转入金额：** 141,064个缺失值 (42.23%)
- **近12月活期转入笔数：** 141,064个缺失值 (42.23%)
- **近12月代扣公积金金额：** 323,501个缺失值 (96.86%)
- **近12月代扣公积金笔数：** 323,501个缺失值 (96.86%)
- **近12月代发工资金额：** 307,847个缺失值 (92.17%)
- **近12月代发工资笔数：** 299,912个缺失值 (89.79%)

## 2. 目标变量分析

目标变量 **'是否购买理财'** 的分布情况：

- **未知：** 332,172个样本 (99.45%)
- **1：** 1,829个样本 (0.55%)

**样本不平衡比例：** 181.6:1

## 3. 特征分析

### 数值型特征 (41个)

主要数值型特征包括：
1. 客户号
2. 存贷款EVA
3. 当年月均交易笔数
4. 表内贷款余额年日均
5. 融资年日均
6. 大额存单年日均余额
7. 结构性存款年日均余额
8. 存款总额年日均
9. 持有产品总数
10. 企业网银-本年登录次数
11. 国际结算-本年交易量折美元
12. 即期结售汇-本年交易量折美元
13. 线上供应链-余额
14. 线上供应链-本年发生额
15. 国内保函-余额
16. 银承贴现-余额
17. 银承贴现-本年发生额
18. 一般贷款余额
19. 一般贷款本年发生额
20. 贷款本年发生额
21. 贷款余额
22. 近12月活期转出金额
23. 近12月活期转出笔数
24. 近12月活期转入金额
25. 近12月活期转入笔数
26. 近12月活期交易总金额
27. 近12月活期交易总笔数
28. 近12月代扣公积金金额
29. 近12月代扣公积金笔数
30. 近12月代发工资金额
31. 近12月代发工资笔数
32. 现金管理-银企直连-是否本年使用
33. 代扣公积金-是否本年使用
34. 代扣税费-是否本年签约
35. 代扣税费-是否本年使用
36. 自助缴费-是否本年使用
37. 收银宝-是否本年使用
38. 代发工资-是否本年签约
39. 代发工资-是否本年使用
40. 是否活跃客户
41. 是否同业

### 分类型特征 (7个)

主要分类型特征包括：
1. 首个账户开户日 (唯一值: 7196个)
2. 是否销户 (唯一值: 3个)
3. 是否有贷户 (唯一值: 2个)
4. 是否当年新开户 (唯一值: 2个)
5. 是否达标有效户 (唯一值: 2个)
6. 是否达标价值户 (唯一值: 2个)
7. 是否代发客户 (唯一值: 2个)

## 4. 关键发现

### 数据质量
⚠️ 存在缺失值，需要在预处理阶段处理

### 样本分布
- 已标注样本：1,829个
- 未标注样本：332,172个
- 适合使用PU学习方法进行建模

### 建议
1. **数据预处理：** 处理缺失值和异常值
2. **特征工程：** 对分类变量进行编码
3. **模型选择：** 考虑使用PU学习处理未标注样本
4. **特征选择：** 基于业务理解和统计方法筛选重要特征

---

*本报告由数据探索分析模块自动生成*
