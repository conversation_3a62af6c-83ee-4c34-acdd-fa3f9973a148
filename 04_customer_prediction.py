#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块4：潜在客户预测模型
功能：使用EasyEnsemble和LightGBM进行潜在客户购买预测
作者：AI助手
创建时间：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import json
import pickle
from datetime import datetime
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import (classification_report, confusion_matrix, roc_auc_score, 
                           f1_score, precision_recall_curve, roc_curve, matthews_corrcoef)
from sklearn.ensemble import AdaBoostClassifier
import lightgbm as lgb
from imblearn.metrics import geometric_mean_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CustomerPredictionModel:
    """
    客户预测模型类
    使用EasyEnsemble处理不平衡数据，LightGBM进行预测
    """
    
    def __init__(self, labeled_data_path, features_path, output_dir):
        """
        初始化客户预测模型
        
        参数说明：
        labeled_data_path (str): 标注数据文件路径
        features_path (str): 最终特征列表文件路径
        output_dir (str): 输出目录路径
        """
        self.labeled_data_path = labeled_data_path
        self.features_path = features_path
        self.output_dir = output_dir
        self.labeled_data = None
        self.features = None
        self.models = []  # 存储EasyEnsemble的多个模型
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """
        加载标注数据和特征列表
        """
        try:
            print("正在加载标注数据和特征列表...")
            
            # 加载标注数据
            self.labeled_data = pd.read_csv(self.labeled_data_path, encoding='utf-8-sig')
            print(f"标注数据加载成功！数据形状：{self.labeled_data.shape}")
            
            # 加载特征列表
            with open(self.features_path, 'r', encoding='utf-8') as f:
                self.features = json.load(f)
            print(f"特征列表加载成功！特征数量：{len(self.features)}")
            
            # 检查目标变量分布
            target_counts = self.labeled_data['是否购买理财'].value_counts()
            print(f"目标变量分布：")
            for value, count in target_counts.items():
                print(f"- {value}: {count}个")
            
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def easy_ensemble_sampling(self, n_estimators=10):
        """
        EasyEnsemble采样
        对多数类进行多次随机欠采样，生成多个平衡的数据子集
        
        参数说明：
        n_estimators (int): 子分类器数量
        
        返回值：
        list: 包含多个平衡数据集的列表
        """
        print("\n" + "="*50)
        print("开始EasyEnsemble采样...")
        
        target_col = '是否购买理财'
        
        # 分离正负样本
        positive_data = self.labeled_data[self.labeled_data[target_col] == 1]
        negative_data = self.labeled_data[self.labeled_data[target_col] == 0]
        
        print(f"正样本数量：{len(positive_data)}")
        print(f"负样本数量：{len(negative_data)}")
        
        # 计算每个子集的负样本数量（与正样本数量相等）
        n_negative_per_subset = len(positive_data)
        
        balanced_datasets = []
        
        for i in range(n_estimators):
            # 随机采样负样本
            sampled_negative = negative_data.sample(n=n_negative_per_subset, random_state=42+i)
            
            # 合并正负样本
            balanced_data = pd.concat([positive_data, sampled_negative], ignore_index=True)
            
            # 打乱数据
            balanced_data = balanced_data.sample(frac=1, random_state=42+i).reset_index(drop=True)
            
            balanced_datasets.append(balanced_data)
            
            print(f"子集{i+1}: 正样本{len(positive_data)}个, 负样本{len(sampled_negative)}个")
        
        print(f"✅ EasyEnsemble采样完成，生成{n_estimators}个平衡数据集")
        
        return balanced_datasets
    
    def train_base_classifier(self, data, classifier_type='lightgbm'):
        """
        训练基分类器
        
        参数说明：
        data (pd.DataFrame): 训练数据
        classifier_type (str): 分类器类型 ('lightgbm' 或 'adaboost')
        
        返回值：
        object: 训练好的分类器
        """
        target_col = '是否购买理财'
        X = data[self.features]
        y = data[target_col]
        
        if classifier_type == 'lightgbm':
            # LightGBM分类器
            train_data = lgb.Dataset(X, label=y)
            params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            model = lgb.train(params, train_data, num_boost_round=100)
            
        elif classifier_type == 'adaboost':
            # AdaBoost分类器
            model = AdaBoostClassifier(
                n_estimators=50,
                learning_rate=1.0,
                random_state=42
            )
            model.fit(X, y)
        
        return model
    
    def train_easy_ensemble(self, classifier_type='lightgbm', n_estimators=10):
        """
        训练EasyEnsemble模型
        
        参数说明：
        classifier_type (str): 基分类器类型
        n_estimators (int): 子分类器数量
        
        返回值：
        bool: 训练是否成功
        """
        try:
            print("\n" + "="*50)
            print("开始训练EasyEnsemble模型...")
            
            # 用户选择基分类器类型
            print(f"\n可选的基分类器类型：")
            print("1. lightgbm - LightGBM")
            print("2. adaboost - AdaBoost")
            
            classifier_choice = input(f"\n请选择基分类器类型 (默认: {classifier_type}): ").strip()
            if classifier_choice in ['lightgbm', 'adaboost']:
                classifier_type = classifier_choice
            
            print(f"选择的基分类器：{classifier_type}")
            
            # 生成平衡数据集
            balanced_datasets = self.easy_ensemble_sampling(n_estimators)
            
            # 训练多个基分类器
            self.models = []
            for i, data in enumerate(balanced_datasets):
                print(f"训练第{i+1}个基分类器...")
                model = self.train_base_classifier(data, classifier_type)
                self.models.append(model)
            
            print(f"✅ EasyEnsemble模型训练完成，共训练{len(self.models)}个基分类器")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型训练失败：{str(e)}")
            return False
    
    def predict_ensemble(self, X):
        """
        EasyEnsemble集成预测
        
        参数说明：
        X (pd.DataFrame): 预测数据
        
        返回值：
        tuple: (预测概率, 预测标签)
        """
        if len(self.models) == 0:
            raise ValueError("模型尚未训练")
        
        # 收集所有基分类器的预测概率
        all_probas = []
        
        for model in self.models:
            if hasattr(model, 'predict'):  # LightGBM
                proba = model.predict(X)
            else:  # AdaBoost
                proba = model.predict_proba(X)[:, 1]
            
            all_probas.append(proba)
        
        # 平均概率（软投票）
        ensemble_proba = np.mean(all_probas, axis=0)
        
        # 预测标签
        ensemble_pred = (ensemble_proba > 0.5).astype(int)
        
        # 硬投票的代码（注释版本）
        # all_preds = []
        # for model in self.models:
        #     if hasattr(model, 'predict'):  # LightGBM
        #         pred = (model.predict(X) > 0.5).astype(int)
        #     else:  # AdaBoost
        #         pred = model.predict(X)
        #     all_preds.append(pred)
        # 
        # # 硬投票：多数投票决定最终预测
        # ensemble_pred = np.round(np.mean(all_preds, axis=0)).astype(int)
        
        return ensemble_proba, ensemble_pred
    
    def evaluate_model(self):
        """
        模型评估
        使用分层交叉验证评估模型性能
        """
        print("\n" + "="*50)
        print("开始模型评估...")
        
        target_col = '是否购买理财'
        X = self.labeled_data[self.features]
        y = self.labeled_data[target_col]
        
        # 分层交叉验证
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        # 评估指标
        auc_scores = []
        f1_scores = []
        gmean_scores = []
        mcc_scores = []
        
        fold = 1
        for train_idx, val_idx in skf.split(X, y):
            print(f"评估第{fold}折...")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 创建训练数据
            train_data = pd.concat([X_train, y_train], axis=1)
            
            # 临时训练模型
            temp_models = []
            balanced_datasets = self.easy_ensemble_sampling_for_fold(train_data, n_estimators=5)
            
            for data in balanced_datasets:
                model = self.train_base_classifier(data, 'lightgbm')
                temp_models.append(model)
            
            # 预测
            all_probas = []
            for model in temp_models:
                proba = model.predict(X_val)
                all_probas.append(proba)
            
            ensemble_proba = np.mean(all_probas, axis=0)
            ensemble_pred = (ensemble_proba > 0.5).astype(int)
            
            # 计算评估指标
            auc = roc_auc_score(y_val, ensemble_proba)
            f1 = f1_score(y_val, ensemble_pred)
            gmean = geometric_mean_score(y_val, ensemble_pred)
            mcc = matthews_corrcoef(y_val, ensemble_pred)
            
            auc_scores.append(auc)
            f1_scores.append(f1)
            gmean_scores.append(gmean)
            mcc_scores.append(mcc)
            
            print(f"第{fold}折结果 - AUC: {auc:.4f}, F1: {f1:.4f}, G-Mean: {gmean:.4f}, MCC: {mcc:.4f}")
            fold += 1
        
        # 计算平均性能
        avg_auc = np.mean(auc_scores)
        avg_f1 = np.mean(f1_scores)
        avg_gmean = np.mean(gmean_scores)
        avg_mcc = np.mean(mcc_scores)
        
        print(f"\n交叉验证平均结果：")
        print(f"- AUC: {avg_auc:.4f} ± {np.std(auc_scores):.4f}")
        print(f"- F1 Score: {avg_f1:.4f} ± {np.std(f1_scores):.4f}")
        print(f"- G-Mean: {avg_gmean:.4f} ± {np.std(gmean_scores):.4f}")
        print(f"- MCC: {avg_mcc:.4f} ± {np.std(mcc_scores):.4f}")
        
        # 保存评估结果
        evaluation_results = {
            'AUC': {'mean': avg_auc, 'std': np.std(auc_scores), 'scores': auc_scores},
            'F1': {'mean': avg_f1, 'std': np.std(f1_scores), 'scores': f1_scores},
            'G-Mean': {'mean': avg_gmean, 'std': np.std(gmean_scores), 'scores': gmean_scores},
            'MCC': {'mean': avg_mcc, 'std': np.std(mcc_scores), 'scores': mcc_scores}
        }
        
        eval_file = os.path.join(self.output_dir, f"{self.timestamp}_evaluation_results.json")
        with open(eval_file, 'w', encoding='utf-8') as f:
            json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 模型评估完成，结果已保存至：{eval_file}")
        
        return evaluation_results

    def easy_ensemble_sampling_for_fold(self, data, n_estimators=5):
        """
        为交叉验证折生成EasyEnsemble采样
        """
        target_col = '是否购买理财'

        positive_data = data[data[target_col] == 1]
        negative_data = data[data[target_col] == 0]

        n_negative_per_subset = len(positive_data)
        balanced_datasets = []

        for i in range(n_estimators):
            if len(negative_data) >= n_negative_per_subset:
                sampled_negative = negative_data.sample(n=n_negative_per_subset, random_state=42+i)
            else:
                sampled_negative = negative_data

            balanced_data = pd.concat([positive_data, sampled_negative], ignore_index=True)
            balanced_data = balanced_data.sample(frac=1, random_state=42+i).reset_index(drop=True)
            balanced_datasets.append(balanced_data)

        return balanced_datasets

    def generate_evaluation_plots(self):
        """
        生成模型评估图表
        """
        print("\n" + "="*50)
        print("生成模型评估图表...")

        target_col = '是否购买理财'
        X = self.labeled_data[self.features]
        y = self.labeled_data[target_col]

        # 分割数据用于绘图
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # 预测测试集
        test_proba, test_pred = self.predict_ensemble(X_test)

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 混淆矩阵
        cm = confusion_matrix(y_test, test_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
        axes[0, 0].set_title('混淆矩阵', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('预测标签')
        axes[0, 0].set_ylabel('真实标签')

        # 2. ROC曲线
        fpr, tpr, _ = roc_curve(y_test, test_proba)
        auc = roc_auc_score(y_test, test_proba)
        axes[0, 1].plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC曲线 (AUC = {auc:.3f})')
        axes[0, 1].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        axes[0, 1].set_xlim([0.0, 1.0])
        axes[0, 1].set_ylim([0.0, 1.05])
        axes[0, 1].set_xlabel('假正率')
        axes[0, 1].set_ylabel('真正率')
        axes[0, 1].set_title('ROC曲线', fontsize=14, fontweight='bold')
        axes[0, 1].legend(loc="lower right")
        axes[0, 1].grid(True, alpha=0.3)

        # 3. PR曲线
        precision, recall, _ = precision_recall_curve(y_test, test_proba)
        axes[1, 0].plot(recall, precision, color='blue', lw=2)
        axes[1, 0].set_xlabel('召回率')
        axes[1, 0].set_ylabel('精确率')
        axes[1, 0].set_title('Precision-Recall曲线', fontsize=14, fontweight='bold')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 预测概率分布
        axes[1, 1].hist(test_proba[y_test == 0], bins=30, alpha=0.7, label='负样本', color='lightcoral', density=True)
        axes[1, 1].hist(test_proba[y_test == 1], bins=30, alpha=0.7, label='正样本', color='lightblue', density=True)
        axes[1, 1].set_xlabel('预测概率')
        axes[1, 1].set_ylabel('密度')
        axes[1, 1].set_title('预测概率分布', fontsize=14, fontweight='bold')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        plot_file = os.path.join(self.output_dir, f"{self.timestamp}_evaluation_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 评估图表已保存至：{plot_file}")

        return plot_file

    def predict_unknown_customers(self, original_data_path):
        """
        对原始数据中的未知客户进行预测

        参数说明：
        original_data_path (str): 原始预处理数据路径

        返回值：
        str: 预测结果文件路径
        """
        print("\n" + "="*50)
        print("对未知客户进行预测...")

        # 加载原始数据
        original_data = pd.read_csv(original_data_path, encoding='utf-8-sig')

        # 筛选未知客户
        unknown_customers = original_data[original_data['是否购买理财'] == '未知'].copy()
        print(f"未知客户数量：{len(unknown_customers)}")

        # 预测
        X_unknown = unknown_customers[self.features]
        unknown_proba, unknown_pred = self.predict_ensemble(X_unknown)

        # 添加预测结果
        unknown_customers['购买概率'] = unknown_proba
        unknown_customers['预测标签'] = unknown_pred

        # 选择输出列
        output_cols = ['客户号'] + self.features + ['购买概率', '预测标签']
        prediction_results = unknown_customers[output_cols].copy()

        # 按购买概率降序排列
        prediction_results = prediction_results.sort_values('购买概率', ascending=False)

        # 保存预测结果
        prediction_file = os.path.join(self.output_dir, "prediction_scores.csv")
        prediction_results.to_csv(prediction_file, index=False, encoding='utf-8-sig')

        print(f"✅ 预测完成，结果已保存至：{prediction_file}")

        # 统计预测结果
        high_potential = (unknown_proba > 0.7).sum()
        medium_potential = ((unknown_proba > 0.3) & (unknown_proba <= 0.7)).sum()
        low_potential = (unknown_proba <= 0.3).sum()

        print(f"\n预测结果统计：")
        print(f"- 高潜力客户 (概率 > 0.7): {high_potential}个")
        print(f"- 中等潜力客户 (0.3 < 概率 <= 0.7): {medium_potential}个")
        print(f"- 低潜力客户 (概率 <= 0.3): {low_potential}个")

        return prediction_file

    def save_model(self):
        """
        保存训练好的模型
        """
        print("\n" + "="*50)
        print("保存训练好的模型...")

        model_data = {
            'models': self.models,
            'features': self.features,
            'timestamp': self.timestamp
        }

        model_file = os.path.join(self.output_dir, "lightgbm_model.pkl")
        with open(model_file, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"✅ 模型已保存至：{model_file}")

        return model_file

    def generate_prediction_report(self):
        """
        生成预测分析报告
        """
        print("\n" + "="*50)
        print("生成预测分析报告...")

        report_file = os.path.join(self.output_dir, f"{self.timestamp}_prediction_report.md")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 潜在客户预测分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            # 模型概述
            f.write("## 1. 模型概述\n\n")
            f.write("### 技术方案\n\n")
            f.write("- **不平衡数据处理：** EasyEnsemble方法\n")
            f.write("- **基分类器：** LightGBM\n")
            f.write("- **集成策略：** 软投票（概率平均）\n")
            f.write("- **验证方法：** 5折分层交叉验证\n\n")

            # EasyEnsemble方法说明
            f.write("### EasyEnsemble方法说明\n\n")
            f.write("EasyEnsemble是一种专门处理不平衡数据的集成学习方法：\n\n")
            f.write("1. **多次欠采样：** 对多数类（负样本）进行多次随机欠采样\n")
            f.write("2. **平衡子集：** 每次采样都与少数类（正样本）构成平衡的数据子集\n")
            f.write("3. **多模型训练：** 在每个平衡子集上训练一个基分类器\n")
            f.write("4. **集成预测：** 将多个基分类器的预测结果进行平均或投票\n\n")

            # 特征信息
            if self.features:
                f.write("## 2. 特征信息\n\n")
                f.write(f"**使用特征数量：** {len(self.features)}个\n\n")
                f.write("**主要特征：**\n")
                for i, feature in enumerate(self.features[:10]):
                    f.write(f"{i+1}. {feature}\n")
                if len(self.features) > 10:
                    f.write(f"... 还有{len(self.features) - 10}个特征\n")
                f.write("\n")

            # 模型性能
            f.write("## 3. 模型性能\n\n")
            f.write("### 评估指标说明\n\n")
            f.write("- **AUC：** 受试者工作特征曲线下面积，衡量模型区分能力\n")
            f.write("- **F1 Score：** 精确率和召回率的调和平均，平衡考虑两个指标\n")
            f.write("- **G-Mean：** 几何平均数，适用于不平衡数据的评估\n")
            f.write("- **MCC：** 马修斯相关系数，考虑真正例、假正例、真负例、假负例\n\n")

            # 业务应用
            f.write("## 4. 业务应用建议\n\n")
            f.write("### 客户分层策略\n\n")
            f.write("根据预测概率对客户进行分层管理：\n\n")
            f.write("- **高潜力客户 (概率 > 0.7)：** 重点营销，个性化服务\n")
            f.write("- **中等潜力客户 (0.3 < 概率 ≤ 0.7)：** 定期跟进，培育转化\n")
            f.write("- **低潜力客户 (概率 ≤ 0.3)：** 基础维护，长期培养\n\n")

            f.write("### 营销策略建议\n\n")
            f.write("1. **精准营销：** 针对高潜力客户制定专门的营销方案\n")
            f.write("2. **资源配置：** 根据客户潜力合理分配营销资源\n")
            f.write("3. **效果监控：** 定期跟踪预测效果，优化模型参数\n")
            f.write("4. **反馈循环：** 收集营销结果，更新训练数据\n\n")

            # 注意事项
            f.write("## 5. 注意事项\n\n")
            f.write("1. **模型更新：** 建议定期使用新数据重新训练模型\n")
            f.write("2. **特征监控：** 关注特征分布的变化，及时调整\n")
            f.write("3. **业务验证：** 结合业务专家经验验证预测结果\n")
            f.write("4. **伦理考量：** 确保模型使用符合相关法规要求\n\n")

            f.write("---\n\n")
            f.write("*本报告由潜在客户预测模块自动生成*\n")

        print(f"预测分析报告已保存至：{report_file}")
        return report_file

    def run_complete_prediction(self):
        """
        运行完整的客户预测流程

        返回值：
        bool: 预测是否成功完成
        """
        try:
            print("开始执行完整的客户预测流程...")
            print("="*60)

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 训练EasyEnsemble模型
            if not self.train_easy_ensemble():
                return False

            # 3. 模型评估
            self.evaluate_model()

            # 4. 生成评估图表
            self.generate_evaluation_plots()

            # 5. 预测未知客户
            original_data_path = "output4/02_processing/processed_data.csv"
            self.predict_unknown_customers(original_data_path)

            # 6. 保存模型
            self.save_model()

            # 7. 生成预测报告
            self.generate_prediction_report()

            print("\n" + "="*60)
            print("✅ 客户预测流程完成！")
            print(f"所有结果已保存至：{self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 预测过程中出现错误：{str(e)}")
            return False


def main():
    """
    主函数：执行客户预测

    使用说明：
    1. 确保标注数据和特征列表文件存在
    2. 根据提示选择基分类器类型
    3. 查看生成的预测结果和评估报告

    迁移到其他项目时需要修改的部分：
    - labeled_data_path: 修改为实际的标注数据文件路径
    - features_path: 修改为实际的特征列表文件路径
    - output_dir: 修改为期望的输出目录
    - original_data_path: 修改为原始预处理数据路径
    - 根据业务需求调整模型参数和评估指标
    """

    # 配置参数（迁移时需要修改这些路径）
    labeled_data_path = "output4/03_feature_selection/labeled_samples.csv"  # 标注数据路径
    features_path = "output4/03_feature_selection/final_features.json"  # 特征列表路径
    output_dir = "output4/04_prediction"  # 输出目录

    # 创建客户预测模型实例
    predictor = CustomerPredictionModel(labeled_data_path, features_path, output_dir)

    # 执行完整预测流程
    success = predictor.run_complete_prediction()

    if success:
        print("\n🎉 客户预测成功完成！")
        print("📊 请查看生成的预测结果和评估报告")
        print(f"📁 预测结果文件：{output_dir}/prediction_scores.csv")
        print(f"🤖 模型文件：{output_dir}/lightgbm_model.pkl")
    else:
        print("\n❌ 客户预测失败，请检查错误信息")


if __name__ == "__main__":
    main()
