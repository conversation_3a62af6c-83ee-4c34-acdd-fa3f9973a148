#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块6：结果报告与看板交付
功能：Flask Web应用，展示预测和聚类分析结果
作者：AI助手
创建时间：2025年
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify
import warnings
warnings.filterwarnings('ignore')

class ResultsDashboard:
    """
    结果看板类
    创建Flask Web应用展示分析结果
    """
    
    def __init__(self, output_base_dir="output4"):
        """
        初始化结果看板
        
        参数说明：
        output_base_dir (str): 输出基础目录
        """
        self.output_base_dir = output_base_dir
        self.prediction_data = None
        self.clustering_data = None
        self.processed_data = None  # 模块6增强：新增处理后数据
        self.features_data = None   # 特征列表数据
        self.app = Flask(__name__)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 设置路由
        self.setup_routes()
        
    def load_data(self):
        """
        加载预测和聚类结果数据
        """
        try:
            print("正在加载分析结果数据...")
            
            # 加载预测结果
            prediction_file = os.path.join(self.output_base_dir, "04_prediction", "prediction_scores.csv")
            if os.path.exists(prediction_file):
                self.prediction_data = pd.read_csv(prediction_file, encoding='utf-8-sig')
                print(f"预测结果加载成功：{len(self.prediction_data)}条记录")
            else:
                print("警告：未找到预测结果文件")
            
            # 加载聚类结果
            clustering_file = os.path.join(self.output_base_dir, "05_clustering", "clustering_results.csv")
            if os.path.exists(clustering_file):
                self.clustering_data = pd.read_csv(clustering_file, encoding='utf-8-sig')
                print(f"聚类结果加载成功：{len(self.clustering_data)}条记录")
            else:
                print("警告：未找到聚类结果文件")

            # 模块6增强：加载处理后数据，数据源使用output4/02_processing/processed_data.csv
            processed_file = os.path.join(self.output_base_dir, "02_processing", "processed_data.csv")
            if os.path.exists(processed_file):
                self.processed_data = pd.read_csv(processed_file, encoding='utf-8-sig')
                print(f"处理后数据加载成功：{len(self.processed_data)}条记录")
            else:
                print("警告：未找到处理后数据文件")

            # 加载特征列表
            features_file = os.path.join(self.output_base_dir, "03_feature_selection", "final_features.json")
            if os.path.exists(features_file):
                with open(features_file, 'r', encoding='utf-8') as f:
                    self.features_data = json.load(f)
                print(f"特征列表加载成功：{len(self.features_data)}个特征")
            else:
                print("警告：未找到特征列表文件")

            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def setup_routes(self):
        """
        设置Flask路由
        """
        
        @self.app.route('/')
        def index():
            """首页"""
            return render_template_string(self.get_index_template())
        
        @self.app.route('/prediction')
        def prediction_report():
            """预测分析报告页面"""
            return render_template_string(self.get_prediction_template())
        
        @self.app.route('/clustering')
        def clustering_report():
            """聚类分析报告页面"""
            return render_template_string(self.get_clustering_template())

        @self.app.route('/processed_eda')
        def processed_eda():
            """模块6增强：新增处理后数据探索分析页面"""
            return render_template_string(self.get_processed_eda_template())
        
        @self.app.route('/api/prediction_data')
        def get_prediction_data():
            """获取预测数据API"""
            if self.prediction_data is None:
                return jsonify({'error': '预测数据未加载'})
            
            # 概率分布统计
            prob_bins = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            prob_counts = pd.cut(self.prediction_data['购买概率'], bins=prob_bins).value_counts().sort_index()
            
            # 高潜力客户统计
            high_potential = len(self.prediction_data[self.prediction_data['购买概率'] > 0.7])
            medium_potential = len(self.prediction_data[
                (self.prediction_data['购买概率'] > 0.3) & 
                (self.prediction_data['购买概率'] <= 0.7)
            ])
            low_potential = len(self.prediction_data[self.prediction_data['购买概率'] <= 0.3])
            
            return jsonify({
                'total_customers': len(self.prediction_data),
                'high_potential': high_potential,
                'medium_potential': medium_potential,
                'low_potential': low_potential,
                'prob_distribution': {
                    'bins': [f"{prob_bins[i]:.1f}-{prob_bins[i+1]:.1f}" for i in range(len(prob_bins)-1)],
                    'counts': prob_counts.tolist()
                },
                'top_customers': self.prediction_data.head(20).to_dict('records')
            })
        
        @self.app.route('/api/clustering_data')
        def get_clustering_data():
            """获取聚类数据API"""
            if self.clustering_data is None:
                return jsonify({'error': '聚类数据未加载'})
            
            # 聚类规模统计
            cluster_counts = self.clustering_data['聚类标签'].value_counts().sort_index()
            
            # 各聚类正样本比例
            cluster_positive_ratios = []
            for cluster_id in sorted(self.clustering_data['聚类标签'].unique()):
                cluster_data = self.clustering_data[self.clustering_data['聚类标签'] == cluster_id]
                if '是否购买理财' in cluster_data.columns:
                    positive_mask = (cluster_data['是否购买理财'] == 1) | (cluster_data['是否购买理财'] == '1')
                    positive_ratio = positive_mask.sum() / len(cluster_data) * 100
                else:
                    positive_ratio = 0
                cluster_positive_ratios.append(positive_ratio)
            
            return jsonify({
                'total_customers': len(self.clustering_data),
                'cluster_counts': {
                    'labels': [f'聚类{i}' for i in cluster_counts.index],
                    'values': cluster_counts.tolist()
                },
                'cluster_positive_ratios': {
                    'labels': [f'聚类{i}' for i in range(len(cluster_positive_ratios))],
                    'values': cluster_positive_ratios
                }
            })
        
        @self.app.route('/api/filter_customers')
        def filter_customers():
            """筛选客户API"""
            if self.prediction_data is None:
                return jsonify({'error': '预测数据未加载'})
            
            min_prob = float(request.args.get('min_prob', 0))
            max_prob = float(request.args.get('max_prob', 1))
            
            filtered_data = self.prediction_data[
                (self.prediction_data['购买概率'] >= min_prob) & 
                (self.prediction_data['购买概率'] <= max_prob)
            ]
            
            return jsonify({
                'count': len(filtered_data),
                'customers': filtered_data.head(100).to_dict('records')
            })

        @self.app.route('/api/processed_data_stats')
        def get_processed_data_stats():
            """获取处理后数据统计信息API"""
            if self.processed_data is None:
                return jsonify({'error': '处理后数据未加载'})

            # 数据处理规范：确保以下特征在所有模块中都被正确识别为分类型特征
            binary_features = [
                '现金管理-银企直连-是否本年使用',
                '代扣公积金-是否本年使用',
                '代扣税费-是否本年签约',
                '代扣税费-是否本年使用',
                '自助缴费-是否本年使用',
                '收银宝-是否本年使用',
                '代发工资-是否本年签约',
                '代发工资-是否本年使用',
                '是否活跃客户'
            ]

            # 排除客户号和目标变量
            exclude_cols = ['是否购买理财', '客户号']

            # 识别数值型特征
            numerical_cols = self.processed_data.select_dtypes(include=[np.number]).columns.tolist()
            numerical_cols = [col for col in numerical_cols if col not in exclude_cols]
            # 排除二元分类特征
            numerical_cols = [col for col in numerical_cols if col not in binary_features]

            # 识别分类型特征
            categorical_cols = [col for col in self.processed_data.columns
                              if col.endswith('_encoded') or col in binary_features]

            # 基本统计信息
            stats = {
                'total_records': len(self.processed_data),
                'total_features': len(self.processed_data.columns) - len(exclude_cols),
                'numerical_features': len(numerical_cols),
                'categorical_features': len(categorical_cols),
                'missing_values': self.processed_data.isnull().sum().sum(),
                'duplicate_records': self.processed_data.duplicated().sum()
            }

            # 目标变量分布
            if '是否购买理财' in self.processed_data.columns:
                target_counts = self.processed_data['是否购买理财'].value_counts()
                stats['target_distribution'] = {
                    'positive': int(target_counts.get(1, 0)),
                    'negative': int(target_counts.get(0, 0))
                }

            # 数值特征统计
            if numerical_cols:
                numerical_stats = self.processed_data[numerical_cols].describe()
                stats['numerical_summary'] = {
                    'features': numerical_cols[:10],  # 前10个特征
                    'means': numerical_stats.loc['mean'].head(10).tolist(),
                    'stds': numerical_stats.loc['std'].head(10).tolist()
                }

            # 分类特征统计
            if categorical_cols:
                categorical_stats = []
                for col in categorical_cols[:10]:  # 前10个特征
                    value_counts = self.processed_data[col].value_counts()
                    categorical_stats.append({
                        'feature': col,
                        'unique_values': len(value_counts),
                        'top_value': value_counts.index[0] if len(value_counts) > 0 else None,
                        'top_count': int(value_counts.iloc[0]) if len(value_counts) > 0 else 0
                    })
                stats['categorical_summary'] = categorical_stats

            return jsonify(stats)

        @self.app.route('/api/feature_correlation')
        def get_feature_correlation():
            """获取特征相关性数据API"""
            if self.processed_data is None or self.features_data is None:
                return jsonify({'error': '数据未加载'})

            # 使用最终特征列表
            features = self.features_data[:20]  # 前20个重要特征

            # 计算相关性矩阵
            correlation_data = self.processed_data[features].corr()

            return jsonify({
                'features': features,
                'correlation_matrix': correlation_data.values.tolist()
            })

        @self.app.route('/api/target_analysis')
        def get_target_analysis():
            """获取目标变量分析数据API"""
            if self.processed_data is None:
                return jsonify({'error': '处理后数据未加载'})

            target_col = '是否购买理财'
            if target_col not in self.processed_data.columns:
                return jsonify({'error': '目标变量不存在'})

            # 按目标变量分组的特征对比
            positive_data = self.processed_data[self.processed_data[target_col] == 1]
            negative_data = self.processed_data[self.processed_data[target_col] == 0]

            # 选择前10个数值特征进行对比
            numerical_cols = self.processed_data.select_dtypes(include=[np.number]).columns.tolist()
            numerical_cols = [col for col in numerical_cols if col not in [target_col, '客户号']]

            comparison_data = []
            for col in numerical_cols[:10]:
                comparison_data.append({
                    'feature': col,
                    'positive_mean': float(positive_data[col].mean()),
                    'negative_mean': float(negative_data[col].mean()),
                    'difference': float(positive_data[col].mean() - negative_data[col].mean())
                })

            return jsonify({
                'feature_comparison': comparison_data,
                'positive_count': len(positive_data),
                'negative_count': len(negative_data)
            })
    
    def get_index_template(self):
        """获取首页模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>理财客户分析看板</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 16px; }
        .card-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .card h3 { color: #2c3e50; margin-bottom: 15px; }
        .card p { color: #7f8c8d; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; transition: background-color 0.3s; }
        .btn:hover { background-color: #2980b9; }
        .btn-secondary { background-color: #95a5a6; }
        .btn-secondary:hover { background-color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>理财客户分析看板</h1>
            <p>基于机器学习的客户预测与聚类分析系统</p>
        </div>
        
        <div class="card-grid">
            <div class="card">
                <h3>📈 处理后数据探索</h3>
                <p>查看数据预处理后的特征分布、相关性分析和目标变量对比</p>
                <a href="/processed_eda" class="btn">数据探索分析</a>
            </div>

            <div class="card">
                <h3>🎯 客户预测分析</h3>
                <p>查看潜在理财客户预测结果，包括购买概率分布、高潜力客户名单等</p>
                <a href="/prediction" class="btn">查看预测报告</a>
            </div>

            <div class="card">
                <h3>👥 客户聚类分析</h3>
                <p>查看客户聚类结果，了解不同客户群体的特征和价值</p>
                <a href="/clustering" class="btn">查看聚类报告</a>
            </div>

            <div class="card">
                <h3>📊 项目概览</h3>
                <p>本项目使用PU学习、EasyEnsemble和K-Means等先进算法进行客户分析</p>
                <a href="#" class="btn btn-secondary">技术文档</a>
            </div>
        </div>
    </div>
</body>
</html>
        """

    def get_processed_eda_template(self):
        """获取处理后数据探索分析模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处理后数据探索分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 8px 16px; margin-right: 10px; background-color: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .nav a:hover { background-color: #2980b9; }
        .nav a.active { background-color: #e74c3c; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
        .chart-container { height: 400px; margin-bottom: 20px; }
        .chart-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>处理后数据探索分析</h1>
            <p>基于processed_data.csv的完整数据分析</p>
        </div>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/processed_eda" class="active">数据探索</a>
            <a href="/prediction">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>

        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <div class="card">
            <h3>数值特征分布</h3>
            <div class="chart-container" id="numericalFeaturesChart"></div>
        </div>

        <div class="card">
            <h3>分类特征分布</h3>
            <div class="chart-container" id="categoricalFeaturesChart"></div>
        </div>

        <div class="card">
            <h3>特征相关性热力图</h3>
            <div class="chart-container" id="correlationHeatmap"></div>
        </div>

        <div class="card">
            <h3>目标变量分析</h3>
            <div class="chart-grid">
                <div>
                    <h4>目标变量分布</h4>
                    <div class="chart-container" id="targetDistributionChart" style="height: 300px;"></div>
                </div>
                <div>
                    <h4>购买vs未购买客户特征对比</h4>
                    <div class="chart-container" id="targetComparisonChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>关键特征雷达图</h3>
            <div class="chart-container" id="radarChart"></div>
        </div>
    </div>

    <script>
        // 加载数据统计信息
        fetch('/api/processed_data_stats')
            .then(response => response.json())
            .then(data => {
                updateStats(data);
                createNumericalFeaturesChart(data.numerical_summary);
                createCategoricalFeaturesChart(data.categorical_summary);
                createTargetDistributionChart(data.target_distribution);
            });

        // 加载特征相关性数据
        fetch('/api/feature_correlation')
            .then(response => response.json())
            .then(data => {
                createCorrelationHeatmap(data);
            });

        // 加载目标变量分析数据
        fetch('/api/target_analysis')
            .then(response => response.json())
            .then(data => {
                createTargetComparisonChart(data.feature_comparison);
                createRadarChart(data.feature_comparison);
            });

        function updateStats(data) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${data.total_records.toLocaleString()}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.total_features}</div>
                    <div class="stat-label">特征数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.numerical_features}</div>
                    <div class="stat-label">数值特征</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.categorical_features}</div>
                    <div class="stat-label">分类特征</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.missing_values}</div>
                    <div class="stat-label">缺失值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.duplicate_records}</div>
                    <div class="stat-label">重复记录</div>
                </div>
            `;
        }

        function createNumericalFeaturesChart(numericalData) {
            if (!numericalData) return;

            const chart = echarts.init(document.getElementById('numericalFeaturesChart'));
            const option = {
                title: { text: '数值特征均值分布', left: 'center' },
                tooltip: { trigger: 'axis' },
                xAxis: {
                    type: 'category',
                    data: numericalData.features,
                    axisLabel: { rotate: 45 }
                },
                yAxis: { type: 'value', name: '均值' },
                series: [{
                    name: '均值',
                    type: 'bar',
                    data: numericalData.means,
                    itemStyle: { color: '#3498db' }
                }]
            };
            chart.setOption(option);
        }

        function createCategoricalFeaturesChart(categoricalData) {
            if (!categoricalData) return;

            const chart = echarts.init(document.getElementById('categoricalFeaturesChart'));
            const option = {
                title: { text: '分类特征唯一值数量', left: 'center' },
                tooltip: { trigger: 'axis' },
                xAxis: {
                    type: 'category',
                    data: categoricalData.map(item => item.feature),
                    axisLabel: { rotate: 45 }
                },
                yAxis: { type: 'value', name: '唯一值数量' },
                series: [{
                    name: '唯一值数量',
                    type: 'bar',
                    data: categoricalData.map(item => item.unique_values),
                    itemStyle: { color: '#e74c3c' }
                }]
            };
            chart.setOption(option);
        }

        function createCorrelationHeatmap(correlationData) {
            if (!correlationData) return;

            const chart = echarts.init(document.getElementById('correlationHeatmap'));

            // 准备热力图数据
            const data = [];
            for (let i = 0; i < correlationData.features.length; i++) {
                for (let j = 0; j < correlationData.features.length; j++) {
                    data.push([i, j, correlationData.correlation_matrix[i][j]]);
                }
            }

            const option = {
                title: { text: '特征相关性热力图', left: 'center' },
                tooltip: {
                    position: 'top',
                    formatter: function(params) {
                        return correlationData.features[params.data[0]] + ' vs ' +
                               correlationData.features[params.data[1]] + ': ' +
                               params.data[2].toFixed(3);
                    }
                },
                grid: { height: '50%', top: '10%' },
                xAxis: {
                    type: 'category',
                    data: correlationData.features,
                    splitArea: { show: true },
                    axisLabel: { rotate: 45 }
                },
                yAxis: {
                    type: 'category',
                    data: correlationData.features,
                    splitArea: { show: true }
                },
                visualMap: {
                    min: -1,
                    max: 1,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '15%',
                    inRange: {
                        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8',
                               '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
                    }
                },
                series: [{
                    name: '相关性',
                    type: 'heatmap',
                    data: data,
                    label: { show: false },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            chart.setOption(option);
        }

        function createTargetDistributionChart(targetData) {
            if (!targetData) return;

            const chart = echarts.init(document.getElementById('targetDistributionChart'));
            const option = {
                title: { text: '目标变量分布', left: 'center' },
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                series: [{
                    name: '客户数量',
                    type: 'pie',
                    radius: '60%',
                    data: [
                        { value: targetData.positive, name: '购买理财' },
                        { value: targetData.negative, name: '未购买理财' }
                    ],
                    emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } }
                }]
            };
            chart.setOption(option);
        }

        function createTargetComparisonChart(comparisonData) {
            if (!comparisonData) return;

            const chart = echarts.init(document.getElementById('targetComparisonChart'));
            const option = {
                title: { text: '特征均值对比', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['购买理财', '未购买理财'], top: 30 },
                xAxis: {
                    type: 'category',
                    data: comparisonData.map(item => item.feature),
                    axisLabel: { rotate: 45 }
                },
                yAxis: { type: 'value', name: '特征值' },
                series: [
                    {
                        name: '购买理财',
                        type: 'bar',
                        data: comparisonData.map(item => item.positive_mean),
                        itemStyle: { color: '#e74c3c' }
                    },
                    {
                        name: '未购买理财',
                        type: 'bar',
                        data: comparisonData.map(item => item.negative_mean),
                        itemStyle: { color: '#3498db' }
                    }
                ]
            };
            chart.setOption(option);
        }

        function createRadarChart(comparisonData) {
            if (!comparisonData) return;

            const chart = echarts.init(document.getElementById('radarChart'));

            // 选择前6个特征进行雷达图展示
            const features = comparisonData.slice(0, 6);

            const option = {
                title: { text: '关键特征雷达图', left: 'center' },
                tooltip: {},
                legend: { data: ['购买理财', '未购买理财'], top: 30 },
                radar: {
                    indicator: features.map(item => ({
                        name: item.feature,
                        max: Math.max(item.positive_mean, item.negative_mean) * 1.2
                    }))
                },
                series: [{
                    name: '客户特征对比',
                    type: 'radar',
                    data: [
                        {
                            value: features.map(item => item.positive_mean),
                            name: '购买理财',
                            itemStyle: { color: '#e74c3c' }
                        },
                        {
                            value: features.map(item => item.negative_mean),
                            name: '未购买理财',
                            itemStyle: { color: '#3498db' }
                        }
                    ]
                }]
            };
            chart.setOption(option);
        }
    </script>
</body>
</html>
        """

    def get_prediction_template(self):
        """获取预测分析模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户预测分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 8px 16px; margin-right: 10px; background-color: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .nav a:hover { background-color: #2980b9; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
        .chart-container { height: 400px; margin-bottom: 20px; }
        .filter-section { margin-bottom: 20px; }
        .filter-section input, .filter-section button { padding: 8px 12px; margin-right: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .filter-section button { background-color: #3498db; color: white; border: none; cursor: pointer; }
        .customer-table { width: 100%; border-collapse: collapse; }
        .customer-table th, .customer-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .customer-table th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>客户预测分析报告</h1>
        </div>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/processed_eda">数据探索</a>
            <a href="/prediction" class="active">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>

        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <div class="card">
            <h3>预测概率分布</h3>
            <div class="chart-container" id="probDistChart"></div>
        </div>

        <div class="card">
            <h3>客户分层统计</h3>
            <div class="chart-container" id="customerSegmentChart"></div>
        </div>

        <div class="card">
            <h3>模型评估结果</h3>
            <div class="chart-container" id="modelEvaluationChart"></div>
        </div>

        <div class="card">
            <h3>高潜力客户名单</h3>
            <div id="customerList"></div>
        </div>
    </div>

    <script>
        // 加载预测数据
        fetch('/api/prediction_data')
            .then(response => response.json())
            .then(data => {
                updateStats(data);
                createProbDistChart(data.prob_distribution);
                createSegmentChart(data);
                createModelEvaluationChart();
                displayTopCustomers(data.top_customers);
            });

        function updateStats(data) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${data.total_customers.toLocaleString()}</div>
                    <div class="stat-label">总潜在客户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.high_potential.toLocaleString()}</div>
                    <div class="stat-label">高潜力客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.medium_potential.toLocaleString()}</div>
                    <div class="stat-label">中等潜力客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${data.low_potential.toLocaleString()}</div>
                    <div class="stat-label">低潜力客户</div>
                </div>
            `;
        }

        function createProbDistChart(probData) {
            const chart = echarts.init(document.getElementById('probDistChart'));
            const option = {
                title: { text: '预测概率分布', left: 'center' },
                tooltip: { trigger: 'axis' },
                xAxis: { type: 'category', data: probData.bins },
                yAxis: { type: 'value', name: '客户数量' },
                series: [{
                    name: '客户数量',
                    type: 'bar',
                    data: probData.counts,
                    itemStyle: { color: '#3498db' }
                }]
            };
            chart.setOption(option);
        }

        function createSegmentChart(data) {
            const chart = echarts.init(document.getElementById('customerSegmentChart'));
            const option = {
                title: { text: '客户分层统计', left: 'center' },
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                series: [{
                    name: '客户分层',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        { value: data.high_potential, name: '高潜力客户' },
                        { value: data.medium_potential, name: '中等潜力客户' },
                        { value: data.low_potential, name: '低潜力客户' }
                    ],
                    emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } }
                }]
            };
            chart.setOption(option);
        }

        function displayTopCustomers(customers) {
            const customerList = document.getElementById('customerList');
            let html = '<h4>高潜力客户（前20名）</h4>';
            html += '<table class="customer-table"><thead><tr><th>客户号</th><th>购买概率</th><th>预测标签</th></tr></thead><tbody>';
            customers.forEach(customer => {
                html += `<tr><td>${customer.客户号}</td><td>${(customer.购买概率 * 100).toFixed(2)}%</td><td>${customer.预测标签}</td></tr>`;
            });
            html += '</tbody></table>';
            customerList.innerHTML = html;
        }

        function filterCustomers() {
            const minProb = document.getElementById('minProb').value;
            const maxProb = document.getElementById('maxProb').value;

            fetch(`/api/filter_customers?min_prob=${minProb}&max_prob=${maxProb}`)
                .then(response => response.json())
                .then(data => {
                    const customerList = document.getElementById('customerList');
                    let html = `<h4>筛选结果（${data.count}个客户）</h4>`;
                    html += '<table class="customer-table"><thead><tr><th>客户号</th><th>购买概率</th><th>预测标签</th></tr></thead><tbody>';
                    data.customers.forEach(customer => {
                        html += `<tr><td>${customer.客户号}</td><td>${(customer.购买概率 * 100).toFixed(2)}%</td><td>${customer.预测标签}</td></tr>`;
                    });
                    html += '</tbody></table>';
                    customerList.innerHTML = html;
                });
        }
    </script>
</body>
</html>
        """

    def get_clustering_template(self):
        """获取聚类分析模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户聚类分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 8px 16px; margin-right: 10px; background-color: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .nav a:hover { background-color: #2980b9; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .chart-container { height: 400px; margin-bottom: 20px; }
        .cluster-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .cluster-card { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 20px; border-radius: 8px; }
        .cluster-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .cluster-stats { font-size: 14px; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>客户聚类分析报告</h1>
        </div>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/prediction">预测分析</a>
            <a href="/clustering">聚类分析</a>
        </div>

        <div class="card">
            <h3>聚类规模分布</h3>
            <div class="chart-container" id="clusterSizeChart"></div>
        </div>

        <div class="card">
            <h3>各聚类购买理财比例</h3>
            <div class="chart-container" id="clusterValueChart"></div>
        </div>

        <div class="cluster-grid" id="clusterGrid">
            <!-- 聚类卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 加载聚类数据
        fetch('/api/clustering_data')
            .then(response => response.json())
            .then(data => {
                createClusterSizeChart(data.cluster_counts);
                createClusterValueChart(data.cluster_positive_ratios);
                displayClusterCards(data);
            });

        function createClusterSizeChart(clusterData) {
            const chart = echarts.init(document.getElementById('clusterSizeChart'));
            const option = {
                title: { text: '各聚类客户数量', left: 'center' },
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                series: [{
                    name: '客户数量',
                    type: 'pie',
                    radius: '50%',
                    data: clusterData.labels.map((label, index) => ({
                        value: clusterData.values[index],
                        name: label
                    })),
                    emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } }
                }]
            };
            chart.setOption(option);
        }

        function createClusterValueChart(ratioData) {
            const chart = echarts.init(document.getElementById('clusterValueChart'));
            const option = {
                title: { text: '各聚类购买理财比例', left: 'center' },
                tooltip: { trigger: 'axis', formatter: '{b}: {c}%' },
                xAxis: { type: 'category', data: ratioData.labels },
                yAxis: { type: 'value', name: '购买比例 (%)' },
                series: [{
                    name: '购买比例',
                    type: 'bar',
                    data: ratioData.values,
                    itemStyle: { color: '#e74c3c' }
                }]
            };
            chart.setOption(option);
        }

        function displayClusterCards(data) {
            const clusterGrid = document.getElementById('clusterGrid');
            let html = '';

            data.cluster_counts.labels.forEach((label, index) => {
                const count = data.cluster_counts.values[index];
                const ratio = data.cluster_positive_ratios.values[index];
                const percentage = ((count / data.total_customers) * 100).toFixed(1);

                html += `
                    <div class="cluster-card">
                        <div class="cluster-title">${label}</div>
                        <div class="cluster-stats">
                            客户数量：${count.toLocaleString()}个 (${percentage}%)<br>
                            购买理财比例：${ratio.toFixed(2)}%
                        </div>
                    </div>
                `;
            });

            clusterGrid.innerHTML = html;
        }
    </script>
</body>
</html>
        """

    def generate_reports(self):
        """
        生成Markdown报告
        """
        print("\n" + "="*50)
        print("生成Markdown报告...")

        # 生成预测分析报告
        prediction_report = self.generate_prediction_report()

        # 生成聚类分析报告
        clustering_report = self.generate_clustering_report()

        print(f"✅ Markdown报告生成完成")
        print(f"预测分析报告：{prediction_report}")
        print(f"聚类分析报告：{clustering_report}")

        return prediction_report, clustering_report

    def generate_prediction_report(self):
        """
        生成预测分析Markdown报告
        """
        # report_file = "output4/prediction_report.md"
        report_file = f"output4/prediction_report_{self.timestamp}.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 理财客户预测分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            if self.prediction_data is not None:
                # 预测结果统计
                total_customers = len(self.prediction_data)
                high_potential = len(self.prediction_data[self.prediction_data['购买概率'] > 0.7])
                medium_potential = len(self.prediction_data[
                    (self.prediction_data['购买概率'] > 0.3) &
                    (self.prediction_data['购买概率'] <= 0.7)
                ])
                low_potential = len(self.prediction_data[self.prediction_data['购买概率'] <= 0.3])

                f.write("## 预测结果概览\n\n")
                f.write(f"- **总潜在客户数：** {total_customers:,}个\n")
                f.write(f"- **高潜力客户：** {high_potential:,}个 ({high_potential/total_customers*100:.1f}%)\n")
                f.write(f"- **中等潜力客户：** {medium_potential:,}个 ({medium_potential/total_customers*100:.1f}%)\n")
                f.write(f"- **低潜力客户：** {low_potential:,}个 ({low_potential/total_customers*100:.1f}%)\n\n")

                # 营销建议
                f.write("## 营销策略建议\n\n")
                f.write("### 高潜力客户群体\n")
                f.write(f"- **数量：** {high_potential:,}个客户\n")
                f.write("- **策略：** 重点营销，提供个性化理财产品和专属服务\n")
                f.write("- **预期转化率：** 较高，建议优先投入营销资源\n\n")

                f.write("### 中等潜力客户群体\n")
                f.write(f"- **数量：** {medium_potential:,}个客户\n")
                f.write("- **策略：** 定期跟进，通过教育和培养提升购买意愿\n")
                f.write("- **预期转化率：** 中等，适合中长期培养\n\n")

                f.write("### 低潜力客户群体\n")
                f.write(f"- **数量：** {low_potential:,}个客户\n")
                f.write("- **策略：** 基础维护，长期培养，避免过度营销\n")
                f.write("- **预期转化率：** 较低，建议降低营销投入\n\n")

            f.write("## 模型技术说明\n\n")
            f.write("- **算法：** EasyEnsemble + LightGBM\n")
            f.write("- **特点：** 专门处理不平衡数据的集成学习方法\n")
            f.write("- **优势：** 提高少数类（购买客户）的识别准确率\n\n")

            f.write("---\n\n")
            f.write("*本报告基于机器学习模型预测结果生成，仅供参考*\n")

        return report_file

    def generate_clustering_report(self):
        """
        生成聚类分析Markdown报告
        """
        report_file = f"output4/clustering_report_{self.timestamp}.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 理财客户聚类分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            if self.clustering_data is not None:
                # 聚类结果统计
                total_customers = len(self.clustering_data)
                cluster_counts = self.clustering_data['聚类标签'].value_counts().sort_index()

                f.write("## 聚类结果概览\n\n")
                f.write(f"- **总潜在客户数：** {total_customers:,}个\n")
                f.write(f"- **聚类数量：** {len(cluster_counts)}个\n\n")

                f.write("### 各聚类规模\n\n")
                for cluster_id, count in cluster_counts.items():
                    percentage = (count / total_customers) * 100
                    f.write(f"- **聚类{cluster_id}：** {count:,}个客户 ({percentage:.1f}%)\n")
                f.write("\n")

                # 各聚类价值分析
                f.write("### 各聚类价值分析\n\n")
                for cluster_id in sorted(cluster_counts.index):
                    cluster_data = self.clustering_data[self.clustering_data['聚类标签'] == cluster_id]
                    if '是否购买理财' in cluster_data.columns:
                        positive_mask = (cluster_data['是否购买理财'] == 1) | (cluster_data['是否购买理财'] == '1')
                        positive_ratio = positive_mask.sum() / len(cluster_data) * 100
                    else:
                        positive_ratio = 0

                    f.write(f"**聚类{cluster_id}：**\n")
                    f.write(f"- 客户数量：{len(cluster_data):,}个\n")
                    f.write(f"- 购买理财比例：{positive_ratio:.2f}%\n")

                    if positive_ratio > 2:
                        f.write("- 价值评级：⭐⭐⭐ 高价值客户群\n")
                        f.write("- 营销建议：重点关注，制定专门营销策略\n")
                    elif positive_ratio > 0.5:
                        f.write("- 价值评级：⭐⭐ 潜力客户群\n")
                        f.write("- 营销建议：定期跟进，培养转化\n")
                    else:
                        f.write("- 价值评级：⭐ 普通客户群\n")
                        f.write("- 营销建议：基础维护，长期培养\n")
                    f.write("\n")

            f.write("## 聚类技术说明\n\n")
            f.write("- **算法：** K-Means聚类\n")
            f.write("- **特征：** 基于重要特征进行聚类\n")
            f.write("- **标准化：** 使用StandardScaler进行数据标准化\n")
            f.write("- **K值选择：** 基于轮廓系数确定最优聚类数\n\n")

            f.write("## 业务应用建议\n\n")
            f.write("1. **差异化营销：** 根据聚类结果制定不同的营销策略\n")
            f.write("2. **资源配置：** 优先向高价值客户群投入营销资源\n")
            f.write("3. **产品设计：** 基于客户群特征设计针对性产品\n")
            f.write("4. **客户维护：** 建立分层的客户服务体系\n\n")

            f.write("---\n\n")
            f.write("*本报告基于K-Means聚类分析结果生成，仅供参考*\n")

        return report_file

    def run_dashboard(self, host='127.0.0.1', port=5000, debug=True):
        """
        运行Flask看板应用

        参数说明：
        host (str): 主机地址
        port (int): 端口号
        debug (bool): 是否开启调试模式
        """
        print("\n" + "="*50)
        print("启动理财客户分析看板...")

        # 加载数据
        if not self.load_data():
            print("❌ 数据加载失败，无法启动看板")
            return False

        # 生成报告
        self.generate_reports()

        print(f"\n🚀 看板启动成功！")
        print(f"📊 访问地址：http://{host}:{port}")
        print(f"🎯 预测分析：http://{host}:{port}/prediction")
        print(f"👥 聚类分析：http://{host}:{port}/clustering")
        print("\n按 Ctrl+C 停止服务")

        # 启动Flask应用
        try:
            self.app.run(host=host, port=port, debug=debug)
            return True
        except Exception as e:
            print(f"❌ 看板启动失败：{str(e)}")
            return False


def main():
    """
    主函数：启动结果看板

    使用说明：
    1. 确保所有分析结果文件存在
    2. 运行脚本启动Web看板
    3. 在浏览器中访问看板地址

    迁移到其他项目时需要修改的部分：
    - output_base_dir: 修改为实际的输出基础目录
    - 根据实际的数据结构调整API和模板
    - 根据需要修改主机地址和端口
    """

    # 配置参数（迁移时需要修改）
    output_base_dir = "output4"  # 输出基础目录
    host = '127.0.0.1'  # 主机地址
    port = 5000  # 端口号

    # 创建结果看板实例
    dashboard = ResultsDashboard(output_base_dir)

    # 启动看板
    success = dashboard.run_dashboard(host=host, port=port, debug=False)

    if not success:
        print("\n❌ 看板启动失败，请检查错误信息")


if __name__ == "__main__":
    main()
