#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块整合运行文件
功能：按顺序执行6个分析模块，提供用户交互和错误处理
作者：AI助手
创建时间：2025年
"""

import os
import sys
import time
import subprocess
from datetime import datetime
import logging

class ModuleRunner:
    """
    模块运行器类
    负责按顺序执行所有分析模块，提供进度显示和错误处理
    """
    
    def __init__(self):
        """
        初始化模块运行器
        """
        self.modules = [
            {
                'name': '数据探索分析',
                'file': '01_data_exploration.py',
                'description': '对原始数据进行全面的探索性分析',
                'estimated_time': '2-3分钟',
                'interactive': False
            },
            {
                'name': '数据预处理',
                'file': '02_data_preprocessing.py', 
                'description': '数据清洗、特征工程和预处理',
                'estimated_time': '3-5分钟',
                'interactive': False
            },
            {
                'name': 'PU学习与特征选择',
                'file': '03_pu_learning_feature_selection.py',
                'description': '负样本生成和特征选择',
                'estimated_time': '5-8分钟',
                'interactive': True,
                'interaction_type': 'pu_method'
            },
            {
                'name': '客户预测模型',
                'file': '04_customer_prediction.py',
                'description': 'EasyEnsemble + LightGBM预测模型训练',
                'estimated_time': '8-12分钟',
                'interactive': True,
                'interaction_type': 'prediction_params'
            },
            {
                'name': '客户聚类分析',
                'file': '05_customer_clustering.py',
                'description': 'K-Means客户聚类和画像分析',
                'estimated_time': '3-5分钟',
                'interactive': True,
                'interaction_type': 'clustering_algorithm'
            },
            {
                'name': 'Web看板交付',
                'file': '06_results_dashboard.py',
                'description': 'Flask Web应用和报告生成',
                'estimated_time': '1-2分钟',
                'interactive': False
            }
        ]
        
        self.log_file = f"execution_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.setup_logging()
        
    def setup_logging(self):
        """
        设置日志记录
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def display_welcome(self):
        """
        显示欢迎信息和项目概览
        """
        print("="*80)
        print("🎯 理财客户分析项目 - 模块整合运行器")
        print("="*80)
        print()
        print("📋 项目概览：")
        print("本项目包含6个核心分析模块，将按顺序执行：")
        print()
        
        total_time_min = 0
        total_time_max = 0
        
        for i, module in enumerate(self.modules, 1):
            status = "🔄 交互式" if module['interactive'] else "🤖 自动化"
            print(f"{i}. {module['name']} ({status})")
            print(f"   📝 {module['description']}")
            print(f"   ⏱️  预计时间：{module['estimated_time']}")
            
            # 计算总时间
            time_range = module['estimated_time'].split('-')
            if len(time_range) == 2:
                min_time = int(time_range[0])
                max_time = int(time_range[1].replace('分钟', ''))
                total_time_min += min_time
                total_time_max += max_time
            print()
        
        print(f"📊 预计总执行时间：{total_time_min}-{total_time_max}分钟")
        print()
        print("⚠️  注意事项：")
        print("- 请确保数据文件'宽表-t.csv'存在于当前目录")
        print("- 部分模块需要用户交互选择参数")
        print("- 执行过程中请勿关闭程序")
        print("- 所有日志将记录到", self.log_file)
        print()
    
    def get_user_confirmation(self):
        """
        获取用户确认是否开始执行
        
        返回值：
        bool: 用户是否确认开始
        """
        while True:
            choice = input("🚀 是否开始执行所有模块？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是', '确认']:
                return True
            elif choice in ['n', 'no', '否', '取消']:
                return False
            else:
                print("请输入 y(是) 或 n(否)")
    
    def get_pu_method_choice(self):
        """
        获取PU学习方法选择
        
        返回值：
        str: 选择的方法
        """
        print("\n" + "="*50)
        print("🔧 PU学习方法选择")
        print("="*50)
        print("请选择负样本生成方法：")
        print("1. spy - Spy Technique (间谍技术)")
        print("   原理：将部分正样本作为'间谍'混入未知样本，训练分类器识别可靠负样本")
        print("   优点：理论基础扎实，效果稳定")
        print()
        print("2. isolation - Isolation Forest (孤立森林)")
        print("   原理：基于异常检测，将远离正样本分布的未知样本标记为负样本")
        print("   优点：无需参数调优，适合高维数据")
        print()
        print("3. ocsvm - One-Class SVM (单类支持向量机)")
        print("   原理：使用单类支持向量机识别异常样本作为负样本")
        print("   优点：理论完备，边界清晰")
        print()
        
        while True:
            choice = input("请选择方法 (1-3，默认为1): ").strip()
            if choice == '' or choice == '1':
                return 'spy'
            elif choice == '2':
                return 'isolation'
            elif choice == '3':
                return 'ocsvm'
            else:
                print("请输入有效选项 (1-3)")
    
    def get_prediction_params(self):
        """
        获取预测模型参数
        
        返回值：
        dict: 参数字典
        """
        print("\n" + "="*50)
        print("🔧 EasyEnsemble参数设置")
        print("="*50)
        
        # 可修改参数：子分类器数量
        while True:
            try:
                n_estimators = input("子分类器数量 (默认10，范围5-20): ").strip()
                if n_estimators == '':
                    n_estimators = 10
                else:
                    n_estimators = int(n_estimators)
                    if not 5 <= n_estimators <= 20:
                        print("请输入5-20之间的数值")
                        continue
                break
            except ValueError:
                print("请输入有效的数字")
        
        # 可修改参数：正负样本比例
        print("\n正负样本比例设置：")
        print("1. 1:1 (平衡)")
        print("2. 1:2 (负样本2倍)")
        print("3. 1:3 (负样本3倍)")
        print("4. 1:5 (负样本5倍)")
        
        while True:
            ratio_choice = input("请选择比例 (1-4，默认为1): ").strip()
            if ratio_choice == '' or ratio_choice == '1':
                ratio = 1
                break
            elif ratio_choice == '2':
                ratio = 2
                break
            elif ratio_choice == '3':
                ratio = 3
                break
            elif ratio_choice == '4':
                ratio = 5
                break
            else:
                print("请输入有效选项 (1-4)")
        
        return {
            'n_estimators': n_estimators,
            'negative_ratio': ratio
        }
    
    def get_clustering_algorithm(self):
        """
        获取聚类算法选择
        
        返回值：
        str: 选择的算法
        """
        print("\n" + "="*50)
        print("🔧 聚类算法选择")
        print("="*50)
        print("请选择聚类算法：")
        print("1. kmeans - K-Means聚类")
        print("   特点：经典聚类算法，需要预设聚类数")
        print("   适用：球形聚类，数据分布相对均匀")
        print()
        print("2. dbscan - DBSCAN聚类")
        print("   特点：基于密度的聚类，自动确定聚类数")
        print("   适用：任意形状聚类，能识别噪声点")
        print()
        print("3. both - 两种算法都执行")
        print("   特点：对比分析两种算法的效果")
        print("   适用：深入分析，选择最佳方案")
        print()
        
        while True:
            choice = input("请选择算法 (1-3，默认为1): ").strip()
            if choice == '' or choice == '1':
                return 'kmeans'
            elif choice == '2':
                return 'dbscan'
            elif choice == '3':
                return 'both'
            else:
                print("请输入有效选项 (1-3)")
    
    def execute_module(self, module_info, user_inputs=None):
        """
        执行单个模块
        
        参数说明：
        module_info (dict): 模块信息
        user_inputs (dict): 用户输入参数
        
        返回值：
        bool: 执行是否成功
        """
        module_name = module_info['name']
        module_file = module_info['file']
        
        print(f"\n🚀 开始执行：{module_name}")
        print(f"📁 模块文件：{module_file}")
        print(f"⏱️  预计时间：{module_info['estimated_time']}")
        print("-" * 60)
        
        self.logger.info(f"开始执行模块：{module_name}")
        start_time = time.time()
        
        try:
            # 构建命令
            if module_info['interactive'] and user_inputs:
                # 处理交互式模块的输入
                if module_info['interaction_type'] == 'pu_method':
                    cmd_input = user_inputs['pu_method']
                elif module_info['interaction_type'] == 'prediction_params':
                    cmd_input = 'lightgbm'  # 基分类器选择
                elif module_info['interaction_type'] == 'clustering_algorithm':
                    cmd_input = ''  # K-Means默认执行
                else:
                    cmd_input = ''
                
                # 使用echo传递输入
                if cmd_input:
                    result = subprocess.run(
                        f'echo "{cmd_input}" | python {module_file}',
                        shell=True,
                        capture_output=True,
                        text=True,
                        encoding='utf-8'
                    )
                else:
                    result = subprocess.run(
                        f'python {module_file}',
                        shell=True,
                        capture_output=True,
                        text=True,
                        encoding='utf-8'
                    )
            else:
                # 非交互式模块直接执行
                result = subprocess.run(
                    f'python {module_file}',
                    shell=True,
                    capture_output=True,
                    text=True,
                    encoding='utf-8'
                )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {module_name} 执行成功")
                print(f"⏱️  实际执行时间：{execution_time:.1f}秒")
                self.logger.info(f"模块 {module_name} 执行成功，耗时 {execution_time:.1f}秒")
                
                # 显示部分输出
                if result.stdout:
                    print("📋 执行输出（最后几行）：")
                    output_lines = result.stdout.strip().split('\n')
                    for line in output_lines[-3:]:
                        if line.strip():
                            print(f"   {line}")
                
                return True
            else:
                print(f"❌ {module_name} 执行失败")
                print(f"错误代码：{result.returncode}")
                if result.stderr:
                    print(f"错误信息：{result.stderr}")
                
                self.logger.error(f"模块 {module_name} 执行失败：{result.stderr}")
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ {module_name} 执行异常：{str(e)}")
            self.logger.error(f"模块 {module_name} 执行异常：{str(e)}")
            return False

    def handle_module_failure(self, module_name):
        """
        处理模块执行失败

        参数说明：
        module_name (str): 失败的模块名称

        返回值：
        str: 用户选择的操作 ('continue', 'skip', 'terminate')
        """
        print(f"\n⚠️  模块 '{module_name}' 执行失败")
        print("请选择后续操作：")
        print("1. continue - 重试执行该模块")
        print("2. skip - 跳过该模块，继续执行后续模块")
        print("3. terminate - 终止整个执行流程")

        while True:
            choice = input("请选择操作 (1-3): ").strip()
            if choice == '1':
                return 'continue'
            elif choice == '2':
                return 'skip'
            elif choice == '3':
                return 'terminate'
            else:
                print("请输入有效选项 (1-3)")

    def run_all_modules(self):
        """
        运行所有模块的主函数

        返回值：
        bool: 整体执行是否成功
        """
        # 显示欢迎信息
        self.display_welcome()

        # 获取用户确认
        if not self.get_user_confirmation():
            print("❌ 用户取消执行")
            return False

        # 收集用户交互输入
        user_inputs = {}

        print("\n📝 收集用户参数...")

        # PU学习方法选择
        user_inputs['pu_method'] = self.get_pu_method_choice()

        # 预测模型参数
        user_inputs['prediction_params'] = self.get_prediction_params()

        # 聚类算法选择
        user_inputs['clustering_algorithm'] = self.get_clustering_algorithm()

        print(f"\n✅ 参数收集完成")
        print(f"📋 执行配置：")
        print(f"   - PU学习方法：{user_inputs['pu_method']}")
        print(f"   - 子分类器数量：{user_inputs['prediction_params']['n_estimators']}")
        print(f"   - 正负样本比例：1:{user_inputs['prediction_params']['negative_ratio']}")
        print(f"   - 聚类算法：{user_inputs['clustering_algorithm']}")

        # 开始执行模块
        print(f"\n🎯 开始执行分析流程...")
        print(f"📅 开始时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        total_start_time = time.time()
        successful_modules = 0
        failed_modules = []

        for i, module in enumerate(self.modules, 1):
            print(f"\n{'='*80}")
            print(f"📊 进度：{i}/{len(self.modules)} - {module['name']}")
            print(f"{'='*80}")

            # 执行模块
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                success = self.execute_module(module, user_inputs)

                if success:
                    successful_modules += 1
                    break
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        action = self.handle_module_failure(module['name'])

                        if action == 'continue':
                            print(f"🔄 重试执行模块 '{module['name']}' (第{retry_count + 1}次)")
                            continue
                        elif action == 'skip':
                            print(f"⏭️  跳过模块 '{module['name']}'")
                            failed_modules.append(module['name'])
                            break
                        elif action == 'terminate':
                            print(f"🛑 用户选择终止执行")
                            self.logger.info("用户选择终止执行流程")
                            return False
                    else:
                        print(f"❌ 模块 '{module['name']}' 重试{max_retries}次后仍然失败")
                        failed_modules.append(module['name'])
                        break

        # 执行完成统计
        total_execution_time = time.time() - total_start_time

        print(f"\n{'='*80}")
        print(f"🎉 执行流程完成")
        print(f"{'='*80}")
        print(f"📅 完成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总执行时间：{total_execution_time/60:.1f}分钟")
        print(f"✅ 成功模块：{successful_modules}/{len(self.modules)}")

        if failed_modules:
            print(f"❌ 失败模块：{len(failed_modules)}个")
            for module_name in failed_modules:
                print(f"   - {module_name}")

        print(f"\n📋 详细日志已保存至：{self.log_file}")

        # 生成执行摘要
        self.generate_execution_summary(successful_modules, failed_modules, total_execution_time)

        return len(failed_modules) == 0

    def generate_execution_summary(self, successful_count, failed_modules, total_time):
        """
        生成执行摘要报告

        参数说明：
        successful_count (int): 成功执行的模块数量
        failed_modules (list): 失败的模块列表
        total_time (float): 总执行时间（秒）
        """
        summary_file = f"execution_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# 理财客户分析项目执行摘要\n\n")
            f.write(f"**执行时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            f.write("## 执行结果\n\n")
            f.write(f"- **总模块数：** {len(self.modules)}\n")
            f.write(f"- **成功执行：** {successful_count}\n")
            f.write(f"- **执行失败：** {len(failed_modules)}\n")
            f.write(f"- **总执行时间：** {total_time/60:.1f}分钟\n\n")

            if successful_count == len(self.modules):
                f.write("🎉 **所有模块执行成功！**\n\n")
            else:
                f.write("⚠️ **部分模块执行失败**\n\n")
                if failed_modules:
                    f.write("失败模块列表：\n")
                    for module_name in failed_modules:
                        f.write(f"- {module_name}\n")
                    f.write("\n")

            f.write("## 模块执行详情\n\n")
            for i, module in enumerate(self.modules, 1):
                status = "✅ 成功" if module['name'] not in failed_modules else "❌ 失败"
                f.write(f"{i}. **{module['name']}** - {status}\n")
                f.write(f"   - 描述：{module['description']}\n")
                f.write(f"   - 文件：{module['file']}\n\n")

            f.write("## 后续建议\n\n")
            if len(failed_modules) == 0:
                f.write("✅ 所有模块执行成功，可以查看生成的分析结果：\n")
                f.write("- 访问Web看板：http://127.0.0.1:5000\n")
                f.write("- 查看output4目录下的分析结果\n")
                f.write("- 阅读生成的Markdown报告\n")
            else:
                f.write("⚠️ 部分模块执行失败，建议：\n")
                f.write("1. 检查失败模块的错误日志\n")
                f.write("2. 确认数据文件和依赖包是否正确\n")
                f.write("3. 单独重新执行失败的模块\n")
                f.write("4. 联系技术支持获取帮助\n")

            f.write(f"\n---\n\n")
            f.write(f"*详细执行日志：{self.log_file}*\n")

        print(f"📄 执行摘要已保存至：{summary_file}")


def main():
    """
    主函数：启动模块整合运行器

    使用说明：
    1. 确保所有模块文件存在于当前目录
    2. 确保数据文件'宽表-t.csv'存在
    3. 根据提示进行交互式参数设置
    4. 等待所有模块执行完成

    注意事项：
    - 执行过程中请勿关闭程序
    - 如遇到错误，可选择重试、跳过或终止
    - 所有日志和摘要会自动保存
    """

    print("🚀 启动理财客户分析项目模块整合运行器")

    # 检查必要文件
    required_files = [
        '01_data_exploration.py',
        '02_data_preprocessing.py',
        '03_pu_learning_feature_selection.py',
        '04_customer_prediction.py',
        '05_customer_clustering.py',
        '06_results_dashboard.py',
        '宽表-t.csv'
    ]

    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件：")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件存在后重新运行")
        return False

    # 创建运行器并执行
    runner = ModuleRunner()
    success = runner.run_all_modules()

    if success:
        print("\n🎉 所有模块执行成功！")
        print("📊 可以开始查看分析结果了")
    else:
        print("\n⚠️  部分模块执行失败")
        print("📋 请查看日志文件了解详情")

    return success


if __name__ == "__main__":
    main()
