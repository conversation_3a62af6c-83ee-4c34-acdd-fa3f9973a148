#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块2：数据预处理与特征工程
功能：对理财客户数据进行清洗、预处理和特征工程
作者：AI助手
创建时间：2025年
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings('ignore')

class DataPreprocessor:
    """
    数据预处理类
    用于对理财客户数据进行全面的数据清洗和特征工程
    """
    
    def __init__(self, data_path, output_dir):
        """
        初始化数据预处理器
        
        参数说明：
        data_path (str): 原始数据文件路径，指向宽表.csv文件
        output_dir (str): 输出目录路径，用于保存预处理后的数据
        """
        self.data_path = data_path  # 存储原始数据文件路径
        self.output_dir = output_dir  # 存储输出目录路径
        self.data = None  # 用于存储原始数据
        self.processed_data = None  # 用于存储预处理后的数据
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # 生成时间戳用于文件命名
        self.label_encoders = {}  # 存储标签编码器，用于后续逆转换
        
        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """
        加载原始数据文件
        处理UTF-8 BOM编码问题，确保数据正确读取
        
        返回值：
        bool: 数据加载是否成功
        """
        try:
            print("正在加载原始数据文件...")
            # 使用utf-8-sig编码处理BOM问题
            self.data = pd.read_csv(self.data_path, encoding='utf-8-sig')
            print(f"数据加载成功！数据形状：{self.data.shape}")
            
            # 创建数据副本用于处理
            self.processed_data = self.data.copy()
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def handle_missing_values(self):
        """
        处理缺失值
        根据特征类型采用不同的缺失值处理策略
        """
        print("\n" + "="*50)
        print("开始处理缺失值...")
        
        # 统计处理前的缺失值情况
        missing_before = self.processed_data.isnull().sum()
        missing_cols_before = missing_before[missing_before > 0]
        print(f"处理前：{len(missing_cols_before)}列存在缺失值")
        
        # 获取数值型和分类型列
        numerical_cols = self.processed_data.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = self.processed_data.select_dtypes(include=['object']).columns.tolist()
        
        # 处理数值型特征的缺失值
        for col in numerical_cols:
            if self.processed_data[col].isnull().sum() > 0:
                # 对于数值型特征，使用中位数填充（对异常值更鲁棒）
                median_value = self.processed_data[col].median()
                self.processed_data[col].fillna(median_value, inplace=True)
                print(f"- 数值型特征 '{col}' 使用中位数 {median_value:.2f} 填充缺失值")
        
        # 处理分类型特征的缺失值
        for col in categorical_cols:
            if self.processed_data[col].isnull().sum() > 0:
                # 对于分类型特征，使用众数填充
                mode_value = self.processed_data[col].mode()
                if len(mode_value) > 0:
                    mode_value = mode_value[0]
                    self.processed_data[col].fillna(mode_value, inplace=True)
                    print(f"- 分类型特征 '{col}' 使用众数 '{mode_value}' 填充缺失值")
                else:
                    # 如果没有众数，使用'未知'填充
                    self.processed_data[col].fillna('未知', inplace=True)
                    print(f"- 分类型特征 '{col}' 使用 '未知' 填充缺失值")
        
        # 统计处理后的缺失值情况
        missing_after = self.processed_data.isnull().sum()
        missing_cols_after = missing_after[missing_after > 0]
        print(f"处理后：{len(missing_cols_after)}列存在缺失值")
        
        if len(missing_cols_after) == 0:
            print("✅ 所有缺失值已成功处理")
        else:
            print(f"⚠️ 仍有{len(missing_cols_after)}列存在缺失值，需要进一步检查")
    
    def handle_outliers(self):
        """
        处理异常值
        使用IQR方法识别和处理数值型特征的异常值
        """
        print("\n" + "="*50)
        print("开始处理异常值...")
        
        # 获取数值型列（排除目标变量和ID列）
        numerical_cols = self.processed_data.select_dtypes(include=[np.number]).columns.tolist()
        exclude_cols = ['客户号', '是否购买理财']  # 不处理的列
        numerical_cols = [col for col in numerical_cols if col not in exclude_cols]
        
        outlier_summary = []  # 存储异常值处理摘要
        
        for col in numerical_cols:
            # 计算IQR（四分位距）
            Q1 = self.processed_data[col].quantile(0.25)
            Q3 = self.processed_data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            # 定义异常值边界（1.5倍IQR规则）
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 识别异常值
            outliers_mask = (self.processed_data[col] < lower_bound) | (self.processed_data[col] > upper_bound)
            outliers_count = outliers_mask.sum()
            
            if outliers_count > 0:
                # 使用边界值替换异常值（Winsorization方法）
                self.processed_data.loc[self.processed_data[col] < lower_bound, col] = lower_bound
                self.processed_data.loc[self.processed_data[col] > upper_bound, col] = upper_bound
                
                outlier_summary.append({
                    '特征名': col,
                    '异常值数量': outliers_count,
                    '异常值比例(%)': round((outliers_count / len(self.processed_data)) * 100, 2),
                    '下边界': round(lower_bound, 2),
                    '上边界': round(upper_bound, 2)
                })
                
                print(f"- '{col}': 处理了{outliers_count}个异常值 ({(outliers_count/len(self.processed_data)*100):.2f}%)")
        
        if outlier_summary:
            # 保存异常值处理摘要
            outlier_df = pd.DataFrame(outlier_summary)
            outlier_file = os.path.join(self.output_dir, f"{self.timestamp}_outlier_summary.csv")
            outlier_df.to_csv(outlier_file, index=False, encoding='utf-8-sig')
            print(f"✅ 异常值处理完成，共处理{len(outlier_summary)}个特征的异常值")
        else:
            print("✅ 未发现需要处理的异常值")
    
    def handle_duplicates(self):
        """
        处理重复值
        识别和删除重复的数据记录
        """
        print("\n" + "="*50)
        print("开始处理重复值...")
        
        # 统计重复值
        duplicates_count = self.processed_data.duplicated().sum()
        print(f"发现 {duplicates_count} 条重复记录")
        
        if duplicates_count > 0:
            # 删除重复值，保留第一次出现的记录
            before_shape = self.processed_data.shape
            self.processed_data = self.processed_data.drop_duplicates()
            after_shape = self.processed_data.shape
            
            removed_count = before_shape[0] - after_shape[0]
            print(f"✅ 已删除 {removed_count} 条重复记录")
            print(f"数据形状从 {before_shape} 变为 {after_shape}")
        else:
            print("✅ 未发现重复记录")
    
    def encode_categorical_features(self):
        """
        编码分类型特征
        对分类型特征进行标签编码处理
        """
        print("\n" + "="*50)
        print("开始编码分类型特征...")
        
        # 获取分类型列（排除目标变量）
        categorical_cols = self.processed_data.select_dtypes(include=['object']).columns.tolist()
        if '是否购买理财' in categorical_cols:
            categorical_cols.remove('是否购买理财')  # 目标变量单独处理
        
        encoding_summary = []  # 存储编码摘要
        
        for col in categorical_cols:
            # 获取唯一值数量
            unique_count = self.processed_data[col].nunique()
            
            # 使用标签编码（Label Encoding）
            # 注意：对于高基数分类变量，可能需要考虑其他编码方法
            le = LabelEncoder()
            self.processed_data[col + '_encoded'] = le.fit_transform(self.processed_data[col])
            
            # 保存编码器以备后用
            self.label_encoders[col] = le
            
            encoding_summary.append({
                '原始特征名': col,
                '编码后特征名': col + '_encoded',
                '唯一值数量': unique_count,
                '编码方法': 'Label Encoding'
            })
            
            print(f"- '{col}' -> '{col}_encoded' (唯一值: {unique_count})")
        
        # 删除原始分类型列（保留编码后的列）
        self.processed_data = self.processed_data.drop(columns=categorical_cols)
        
        if encoding_summary:
            # 保存编码摘要
            encoding_df = pd.DataFrame(encoding_summary)
            encoding_file = os.path.join(self.output_dir, f"{self.timestamp}_encoding_summary.csv")
            encoding_df.to_csv(encoding_file, index=False, encoding='utf-8-sig')
            print(f"✅ 分类特征编码完成，共编码{len(encoding_summary)}个特征")
        else:
            print("✅ 未发现需要编码的分类特征")
    
    def data_type_conversion(self):
        """
        数据类型转换
        确保数据类型的一致性和正确性
        """
        print("\n" + "="*50)
        print("开始数据类型转换...")
        
        # 转换布尔型特征为整型（0/1）
        bool_cols = []
        for col in self.processed_data.columns:
            if self.processed_data[col].dtype == 'bool':
                self.processed_data[col] = self.processed_data[col].astype(int)
                bool_cols.append(col)
        
        if bool_cols:
            print(f"- 转换布尔型特征为整型：{bool_cols}")
        
        # 确保数值型特征为适当的数值类型
        for col in self.processed_data.select_dtypes(include=[np.number]).columns:
            # 检查是否可以转换为更小的数据类型以节省内存
            if self.processed_data[col].dtype == 'float64':
                # 检查是否所有值都是整数
                if self.processed_data[col].apply(lambda x: x.is_integer() if pd.notnull(x) else True).all():
                    # 尝试转换为整型
                    try:
                        self.processed_data[col] = self.processed_data[col].astype('int64')
                        print(f"- '{col}' 从 float64 转换为 int64")
                    except:
                        pass  # 如果转换失败，保持原类型
        
        print("✅ 数据类型转换完成")
    
    def save_processed_data(self):
        """
        保存预处理后的数据
        将处理完成的数据保存为CSV文件
        """
        print("\n" + "="*50)
        print("保存预处理后的数据...")
        
        # 文件管理策略：核心数据文件不添加时间戳
        # 原因：后续模块需要固定文件名进行调用
        output_file = os.path.join(self.output_dir, "processed_data.csv")
        self.processed_data.to_csv(output_file, index=False, encoding='utf-8-sig')

        # 文件管理策略：同时生成备份文件，添加时间戳避免覆盖
        # 原因：保留历史版本，便于对比和回滚
        backup_file = os.path.join(self.output_dir, f"processed_data_backup_{self.timestamp}.csv")
        self.processed_data.to_csv(backup_file, index=False, encoding='utf-8-sig')

        # 生成数据处理报告（添加时间戳）
        report_file = os.path.join(self.output_dir, f"{self.timestamp}_preprocessing_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("数据预处理报告\n")
            f.write("="*50 + "\n\n")
            f.write(f"处理时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write(f"原始数据形状：{self.data.shape}\n")
            f.write(f"处理后数据形状：{self.processed_data.shape}\n")
            f.write(f"特征数量变化：{self.data.shape[1]} -> {self.processed_data.shape[1]}\n\n")
            f.write("处理步骤：\n")
            f.write("1. 缺失值处理：使用中位数/众数填充\n")
            f.write("2. 异常值处理：使用IQR方法和Winsorization\n")
            f.write("3. 重复值处理：删除重复记录\n")
            f.write("4. 特征编码：分类变量标签编码\n")
            f.write("5. 数据类型转换：优化内存使用\n\n")
            f.write("文件管理策略说明：\n")
            f.write(f"- 主文件：{output_file} (固定名称，供后续模块调用)\n")
            f.write(f"- 备份文件：{backup_file} (带时间戳，避免覆盖)\n\n")

        print(f"✅ 预处理数据已保存至：{output_file}")
        print(f"💾 备份文件已保存至：{backup_file}")
        print(f"📊 处理报告已保存至：{report_file}")

        return output_file

    def run_complete_preprocessing(self):
        """
        运行完整的数据预处理流程

        返回值：
        tuple: (是否成功, 输出文件路径)
        """
        try:
            print("开始执行完整的数据预处理流程...")
            print("="*60)

            # 1. 加载数据
            if not self.load_data():
                return False, None

            # 2. 处理缺失值
            self.handle_missing_values()

            # 3. 处理异常值
            self.handle_outliers()

            # 4. 处理重复值
            self.handle_duplicates()

            # 5. 编码分类型特征
            self.encode_categorical_features()

            # 6. 数据类型转换
            self.data_type_conversion()

            # 7. 保存预处理后的数据
            output_file = self.save_processed_data()

            # 8. 模块2增强：生成处理后数据分析
            self.generate_processed_data_analysis()

            print("\n" + "="*60)
            print("✅ 数据预处理完成！")
            print(f"原始数据：{self.data.shape[0]}行 × {self.data.shape[1]}列")
            print(f"处理后数据：{self.processed_data.shape[0]}行 × {self.processed_data.shape[1]}列")
            print(f"所有结果已保存至：{self.output_dir}")

            return True, output_file

        except Exception as e:
            print(f"❌ 预处理过程中出现错误：{str(e)}")
            return False, None

    def get_feature_info(self):
        """
        获取预处理后的特征信息

        返回值：
        dict: 包含特征信息的字典
        """
        if self.processed_data is None:
            print("⚠️ 数据尚未预处理，请先运行预处理流程")
            return None

        feature_info = {
            'total_features': self.processed_data.shape[1],
            'numerical_features': self.processed_data.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_features': self.processed_data.select_dtypes(include=['object']).columns.tolist(),
            'target_column': '是否购买理财' if '是否购买理财' in self.processed_data.columns else None,
            'data_shape': self.processed_data.shape
        }

        return feature_info

    def generate_processed_data_analysis(self):
        """
        模块2增强：基于processed_data.csv生成完整的探索性分析

        功能说明：
        - 分析预处理后数据的特征分布
        - 生成处理前后对比图表
        - 输出数值特征统计信息
        """
        print("\n" + "="*50)
        print("开始处理后数据分析...")

        # 导入matplotlib（在方法内导入避免模块级依赖）
        import matplotlib.pyplot as plt

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 获取数值型和分类型特征（排除客户号和目标变量）
        # 数据处理规范：必须排除客户号字段
        exclude_cols = ['是否购买理财', '客户号']

        # 识别数值型特征（排除二元分类特征）
        numerical_cols = self.processed_data.select_dtypes(include=[np.number]).columns.tolist()
        numerical_cols = [col for col in numerical_cols if col not in exclude_cols]
        binary_features = [col for col in numerical_cols if '是否' in col]
        numerical_cols = [col for col in numerical_cols if col not in binary_features]

        # 识别分类型特征（包括编码后的特征和二元特征）
        categorical_cols = [col for col in self.processed_data.columns
                          if col.endswith('_encoded') or col in binary_features]

        print(f"数值型特征：{len(numerical_cols)}个")
        print(f"分类型特征：{len(categorical_cols)}个")

        # 生成数值特征统计
        if len(numerical_cols) > 0:
            numerical_stats = self.processed_data[numerical_cols].describe()
            stats_file = os.path.join(self.output_dir, "numerical_stats_processed.csv")
            numerical_stats.to_csv(stats_file, encoding='utf-8-sig')
            print(f"✅ 处理后数值特征统计已保存至：{stats_file}")

        # 生成特征分布图表
        self._plot_processed_features_distribution(numerical_cols, categorical_cols)

        # 生成处理前后对比分析
        if self.data is not None:
            self._plot_before_after_comparison(numerical_cols)

        print("✅ 处理后数据分析完成")

    def _plot_processed_features_distribution(self, numerical_cols, categorical_cols):
        """
        绘制处理后特征分布图

        参数说明：
        numerical_cols (list): 数值型特征列表
        categorical_cols (list): 分类型特征列表
        """
        import matplotlib.pyplot as plt

        # 绘制数值型特征分布
        if len(numerical_cols) > 0:
            # 子图拆分策略：每个图表最多12个特征
            chunk_size = 12
            for chunk_idx in range(0, len(numerical_cols), chunk_size):
                chunk_cols = numerical_cols[chunk_idx:chunk_idx + chunk_size]

                n_cols = 4
                n_rows = (len(chunk_cols) + n_cols - 1) // n_cols

                plt.figure(figsize=(20, 5*n_rows))

                for i, col in enumerate(chunk_cols):
                    plt.subplot(n_rows, n_cols, i + 1)

                    # 图表质量标准：设置透明度和网格
                    self.processed_data[col].hist(bins=30, alpha=0.7, color='lightblue', edgecolor='black')

                    # 图表质量标准：标题格式
                    plt.title(f'直方图 - {col}分布(处理后)', fontsize=10, fontweight='bold')
                    plt.xlabel(col, fontsize=9)
                    plt.ylabel('频次', fontsize=9)
                    plt.grid(True, alpha=0.3)

                    # 添加统计信息
                    mean_val = self.processed_data[col].mean()
                    std_val = self.processed_data[col].std()
                    plt.text(0.02, 0.98, f'均值: {mean_val:.2f}\n标准差: {std_val:.2f}',
                            transform=plt.gca().transAxes, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

                plt.tight_layout()

                # 保存图表
                plot_file = os.path.join(self.output_dir, f"{self.timestamp}_processed_numerical_dist_part{chunk_idx//chunk_size + 1}.png")
                plt.savefig(plot_file, dpi=300, bbox_inches='tight')
                plt.show()

                print(f"数值特征分布图(第{chunk_idx//chunk_size + 1}部分)已保存至：{plot_file}")

        # 绘制分类型特征分布
        if len(categorical_cols) > 0:
            # 选择前8个分类特征进行可视化
            plot_categorical = categorical_cols[:8]

            n_cols = 2
            n_rows = (len(plot_categorical) + n_cols - 1) // n_cols

            plt.figure(figsize=(15, 6*n_rows))

            for i, col in enumerate(plot_categorical):
                plt.subplot(n_rows, n_cols, i + 1)

                # 计算值计数
                value_counts = self.processed_data[col].value_counts().head(10)

                # 图表质量标准：柱状图显示数值和百分比
                bars = plt.bar(range(len(value_counts)), value_counts.values,
                              color='lightgreen', alpha=0.7)

                # 添加数值标签
                total = value_counts.sum()
                for j, (bar, count) in enumerate(zip(bars, value_counts.values)):
                    percentage = (count / total) * 100
                    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(value_counts.values) * 0.01,
                            f'{count}\n({percentage:.1f}%)', ha='center', va='bottom', fontsize=8)

                plt.title(f'柱状图 - {col}分布(处理后)', fontsize=12, fontweight='bold')
                plt.xlabel('类别', fontsize=10)
                plt.ylabel('频次', fontsize=10)
                plt.xticks(range(len(value_counts)), value_counts.index, rotation=45)
                plt.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            categorical_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_processed_categorical_distributions.png")
            plt.savefig(categorical_plot_file, dpi=300, bbox_inches='tight')
            plt.show()

            print(f"分类特征分布图已保存至：{categorical_plot_file}")

    def _plot_before_after_comparison(self, numerical_cols):
        """
        绘制处理前后对比图

        参数说明：
        numerical_cols (list): 数值型特征列表
        """
        import matplotlib.pyplot as plt

        # 选择前6个数值特征进行对比
        compare_cols = numerical_cols[:6]

        if len(compare_cols) == 0:
            return

        n_cols = 3
        n_rows = (len(compare_cols) + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)

        for i, col in enumerate(compare_cols):
            row = i // n_cols
            col_idx = i % n_cols
            ax = axes[row, col_idx] if n_rows > 1 else axes[col_idx]

            # 检查原始数据中是否存在该列
            if col in self.data.columns:
                # 绘制处理前后对比
                ax.hist(self.data[col].dropna(), bins=30, alpha=0.7,
                       label='处理前', color='lightcoral', density=True)
                ax.hist(self.processed_data[col], bins=30, alpha=0.7,
                       label='处理后', color='lightblue', density=True)

                ax.set_title(f'对比图 - {col}处理前后分布', fontsize=12, fontweight='bold')
                ax.set_xlabel(col, fontsize=10)
                ax.set_ylabel('密度', fontsize=10)
                ax.legend()
                ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        if n_rows > 1:
            for i in range(len(compare_cols), n_rows * n_cols):
                row = i // n_cols
                col_idx = i % n_cols
                axes[row, col_idx].set_visible(False)

        plt.tight_layout()

        # 保存图表
        comparison_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_before_after_comparison.png")
        plt.savefig(comparison_plot_file, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"处理前后对比图已保存至：{comparison_plot_file}")


def main():
    """
    主函数：执行数据预处理

    使用说明：
    1. 确保原始数据文件路径正确
    2. 确保输出目录存在或可创建
    3. 运行脚本即可生成预处理后的数据

    迁移到其他项目时需要修改的部分：
    - data_path: 修改为实际的原始数据文件路径
    - output_dir: 修改为期望的输出目录
    - 目标变量列名（如果不是'是否购买理财'）
    - 特殊列名（如客户ID列名）
    - 根据业务需求调整缺失值和异常值处理策略
    """

    # 配置参数（迁移时需要修改这些路径）
    data_path = "宽表.csv"  # 原始数据文件路径
    output_dir = "output4/02_processing"  # 输出目录

    # 创建数据预处理器实例
    preprocessor = DataPreprocessor(data_path, output_dir)

    # 执行完整预处理
    success, output_file = preprocessor.run_complete_preprocessing()

    if success:
        print("\n🎉 数据预处理成功完成！")
        print(f"📁 预处理后的数据文件：{output_file}")

        # 显示特征信息
        feature_info = preprocessor.get_feature_info()
        if feature_info:
            print(f"\n📊 特征信息摘要：")
            print(f"- 总特征数：{feature_info['total_features']}")
            print(f"- 数值型特征：{len(feature_info['numerical_features'])}个")
            print(f"- 分类型特征：{len(feature_info['categorical_features'])}个")
            print(f"- 数据形状：{feature_info['data_shape']}")
            if feature_info['target_column']:
                print(f"- 目标变量：{feature_info['target_column']}")
    else:
        print("\n❌ 数据预处理失败，请检查错误信息")


if __name__ == "__main__":
    main()
