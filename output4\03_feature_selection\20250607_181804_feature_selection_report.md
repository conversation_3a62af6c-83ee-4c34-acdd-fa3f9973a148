# PU学习与特征选择分析报告

**生成时间：** 2025年06月07日 18:19:13

---

## 1. 负样本生成结果

- **正样本数量：** 1,829个
- **负样本数量：** 262,932个
- **样本比例：** 1:143.8

## 2. 特征选择结果

- **最终特征数量：** 21个
- **选择方法：** IV值筛选 + 模型重要性 + SHAP值 + 排列重要性

### 最终选择的特征列表

1. 存款总额年日均
2. 近12月活期交易总金额
3. 存贷款EVA
4. 近12月活期转出金额
5. 近12月活期转入金额
6. 企业网银-本年登录次数
7. 是否达标有效户_encoded
8. 近12月活期交易总笔数
9. 近12月活期转出笔数
10. 当年月均交易笔数
11. 近12月活期转入笔数
12. 持有产品总数
13. 是否活跃客户
14. 是否达标价值户_encoded
15. 是否销户_encoded
16. 代扣税费-是否本年使用
17. 是否代发客户_encoded
18. 首个账户开户日期_encoded
19. 是否有贷户_encoded
20. 首个账户开户日_encoded
21. 客户开户行

## 3. 方法说明

### 负样本生成方法

本项目提供三种负样本生成方法：

1. **Spy Technique：** 将部分正样本作为'间谍'混入未知样本，训练分类器识别可靠负样本
2. **Isolation Forest：** 基于异常检测，将远离正样本分布的未知样本标记为负样本
3. **One-Class SVM：** 使用单类支持向量机识别异常样本作为负样本

### 特征选择流程

1. **IV值粗筛：** 使用信息价值(Information Value)剔除区分能力弱的特征
2. **模型重要性：** 基于LightGBM模型的特征重要性评估
3. **SHAP值分析：** 使用SHAP值评估特征的全局和局部贡献度
4. **排列重要性：** 通过特征打乱评估对模型性能的影响
5. **综合排名：** 融合多种方法的结果，选择最终特征集

## 4. 业务建议

1. **模型训练：** 使用生成的标注数据进行模型训练
2. **特征监控：** 定期监控特征重要性的变化
3. **样本更新：** 根据业务反馈更新正负样本标注
4. **模型验证：** 使用业务专家知识验证模型结果的合理性

---

*本报告由PU学习与特征选择模块自动生成*
