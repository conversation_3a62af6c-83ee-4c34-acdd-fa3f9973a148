#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块5：客户聚类分析（双算法增强版）
功能：基于重要特征进行K-Means和DBSCAN双算法客户聚类分析
作者：AI助手
创建时间：2025年
"""

import os
# 设置环境变量禁用并行处理
os.environ['LOKY_MAX_CPU_COUNT'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
from datetime import datetime
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
from sklearn.neighbors import NearestNeighbors
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CustomerClustering:
    """
    客户聚类分析类（双算法增强版）
    基于重要特征进行K-Means和DBSCAN双算法聚类分析
    """

    def __init__(self, data_path, features_path, output_dir):
        """
        初始化客户聚类分析器

        参数说明：
        data_path (str): 预处理后的数据文件路径
        features_path (str): 最终特征列表文件路径
        output_dir (str): 输出目录路径
        """
        self.data_path = data_path
        self.features_path = features_path
        self.output_dir = output_dir
        self.data = None
        self.features = None
        self.scaled_data = None
        self.scaler = None

        # K-Means相关属性
        self.kmeans_model = None
        self.optimal_k = None
        self.kmeans_labels = None

        # DBSCAN相关属性
        self.dbscan_model = None
        self.optimal_eps = None
        self.optimal_min_samples = None
        self.dbscan_labels = None

        # 算法对比结果
        self.algorithm_comparison = {}

        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """
        加载数据和特征列表
        """
        try:
            print("正在加载数据和特征列表...")
            
            # 加载预处理后的数据
            self.data = pd.read_csv(self.data_path, encoding='utf-8-sig')
            print(f"数据加载成功！数据形状：{self.data.shape}")
            
            # 加载特征列表
            with open(self.features_path, 'r', encoding='utf-8') as f:
                self.features = json.load(f)
            print(f"特征列表加载成功！特征数量：{len(self.features)}")
            
            # 检查特征是否存在于数据中
            missing_features = [f for f in self.features if f not in self.data.columns]
            if missing_features:
                print(f"警告：以下特征在数据中不存在：{missing_features}")
                self.features = [f for f in self.features if f in self.data.columns]
                print(f"实际使用特征数量：{len(self.features)}")
            
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def standardize_data(self, use_full_data=True):
        """
        数据标准化
        使用StandardScaler对特征进行标准化处理

        参数说明：
        use_full_data (bool): 模块5增强：使用全量数据，不进行采样
        """
        print("\n" + "="*50)
        print("开始数据标准化...")

        # 模块5增强：修改数据使用策略，使用processed_data.csv全量数据，不进行采样
        if use_full_data:
            print(f"使用全量数据进行聚类分析，数据量：{len(self.data)}行")
            print("📊 优势：保留完整的数据分布信息，提高聚类结果的代表性")
        else:
            # 保留采样选项以备特殊情况使用
            sample_size = 50000
            if len(self.data) > sample_size:
                print(f"数据量较大({len(self.data)}行)，采样{sample_size}行进行聚类分析...")
                sampled_data = self.data.sample(n=sample_size, random_state=42)
                self.data = sampled_data

        # 提取特征数据
        feature_data = self.data[self.features]

        # 标准化
        self.scaler = StandardScaler()
        self.scaled_data = self.scaler.fit_transform(feature_data)

        print(f"✅ 数据标准化完成")
        print(f"标准化前数据形状：{feature_data.shape}")
        print(f"标准化后数据形状：{self.scaled_data.shape}")

        # 显示标准化前后的统计信息
        print(f"\n标准化前统计信息：")
        print(f"- 均值范围：[{feature_data.mean().min():.2f}, {feature_data.mean().max():.2f}]")
        print(f"- 标准差范围：[{feature_data.std().min():.2f}, {feature_data.std().max():.2f}]")

        scaled_df = pd.DataFrame(self.scaled_data, columns=self.features)
        print(f"\n标准化后统计信息：")
        print(f"- 均值范围：[{scaled_df.mean().min():.2f}, {scaled_df.mean().max():.2f}]")
        print(f"- 标准差范围：[{scaled_df.std().min():.2f}, {scaled_df.std().max():.2f}]")
    
    def determine_optimal_k(self, k_range=(2, 8)):
        """
        确定最优聚类数K
        使用轮廓系数和肘部法则

        参数说明：
        k_range (tuple): K值搜索范围 # 可修改参数：优化K值选择范围，默认2-8

        返回值：
        int: 最优K值
        """
        print("\n" + "="*50)
        print("确定最优聚类数K...")
        print(f"🔍 K值搜索范围：{k_range[0]}-{k_range[1]-1}")

        k_values = range(k_range[0], k_range[1])
        silhouette_scores = []
        inertias = []

        print("计算不同K值的评估指标...")
        for k in k_values:
            print(f"计算K={k}...")

            # K-Means聚类
            # 可修改参数：K-Means算法参数
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10, max_iter=300)
            cluster_labels = kmeans.fit_predict(self.scaled_data)

            # 计算轮廓系数
            silhouette_avg = silhouette_score(self.scaled_data, cluster_labels)
            silhouette_scores.append(silhouette_avg)

            # 计算惯性（用于肘部法则）
            inertias.append(kmeans.inertia_)

            print(f"K={k}: 轮廓系数={silhouette_avg:.4f}, 惯性={kmeans.inertia_:.2f}")

        # 选择轮廓系数最高的K值
        best_k_idx = np.argmax(silhouette_scores)
        self.optimal_k = k_values[best_k_idx]

        print(f"\n✅ 最优K值确定：K={self.optimal_k}")
        print(f"对应轮廓系数：{silhouette_scores[best_k_idx]:.4f}")
        
        # 绘制K值选择图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 轮廓系数图
        ax1.plot(k_values, silhouette_scores, 'bo-', linewidth=2, markersize=8)
        ax1.axvline(x=self.optimal_k, color='red', linestyle='--', alpha=0.7, 
                   label=f'最优K={self.optimal_k}')
        ax1.set_xlabel('聚类数K')
        ax1.set_ylabel('轮廓系数')
        ax1.set_title('轮廓系数法选择最优K值', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 肘部法则图
        ax2.plot(k_values, inertias, 'ro-', linewidth=2, markersize=8)
        ax2.set_xlabel('聚类数K')
        ax2.set_ylabel('惯性（Within-cluster sum of squares）')
        ax2.set_title('肘部法则选择最优K值', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        k_selection_plot = os.path.join(self.output_dir, f"{self.timestamp}_k_selection.png")
        plt.savefig(k_selection_plot, dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存K值选择结果
        k_results = {
            'k_values': list(k_values),
            'silhouette_scores': silhouette_scores,
            'inertias': inertias,
            'optimal_k': self.optimal_k,
            'best_silhouette_score': silhouette_scores[best_k_idx]
        }
        
        k_results_file = os.path.join(self.output_dir, f"{self.timestamp}_k_selection_results.json")
        with open(k_results_file, 'w', encoding='utf-8') as f:
            json.dump(k_results, f, ensure_ascii=False, indent=2)
        
        print(f"K值选择结果已保存至：{k_results_file}")
        
        return self.optimal_k

    def determine_optimal_dbscan_params(self):
        """
        确定DBSCAN最优参数
        使用K距离图和轮廓系数优化eps和min_samples参数

        返回值：
        tuple: (最优eps, 最优min_samples)
        """
        print("\n" + "="*50)
        print("确定DBSCAN最优参数...")

        # 第一步：使用K距离图估计eps
        print("🔍 第一步：使用K距离图估计eps参数...")

        # 可修改参数：K值，通常设为min_samples-1
        k = 4  # 对应min_samples=5

        # 计算K近邻距离
        neighbors = NearestNeighbors(n_neighbors=k)
        neighbors_fit = neighbors.fit(self.scaled_data)
        distances, indices = neighbors_fit.kneighbors(self.scaled_data)

        # 排序距离
        distances = np.sort(distances[:, k-1], axis=0)

        # 绘制K距离图
        plt.figure(figsize=(10, 6))
        plt.plot(distances)
        plt.xlabel('数据点索引')
        plt.ylabel(f'{k}-距离')
        plt.title(f'K距离图 (K={k}) - 用于估计eps参数', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        # 保存K距离图
        k_distance_plot = os.path.join(self.output_dir, f"{self.timestamp}_k_distance_plot.png")
        plt.savefig(k_distance_plot, dpi=300, bbox_inches='tight')
        plt.show()

        # 自动估计eps（使用距离的90%分位数）
        estimated_eps = np.percentile(distances, 90)
        print(f"📊 基于K距离图估计的eps: {estimated_eps:.4f}")

        # 第二步：网格搜索优化参数
        print("🔍 第二步：网格搜索优化DBSCAN参数...")

        # 可修改参数：参数搜索范围
        eps_range = np.linspace(estimated_eps * 0.5, estimated_eps * 1.5, 10)
        min_samples_range = [3, 5, 7, 10]

        best_score = -1
        best_eps = None
        best_min_samples = None
        best_n_clusters = 0

        results = []

        for eps in eps_range:
            for min_samples in min_samples_range:
                # 执行DBSCAN
                dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                labels = dbscan.fit_predict(self.scaled_data)

                # 计算聚类数量（排除噪声点）
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                n_noise = list(labels).count(-1)

                # 如果聚类数量合理（2-10个）且噪声点不太多
                if 2 <= n_clusters <= 10 and n_noise < len(self.scaled_data) * 0.3:
                    try:
                        # 计算轮廓系数（排除噪声点）
                        if n_clusters > 1:
                            mask = labels != -1
                            if mask.sum() > 1:
                                score = silhouette_score(self.scaled_data[mask], labels[mask])
                            else:
                                score = -1
                        else:
                            score = -1
                    except:
                        score = -1
                else:
                    score = -1

                results.append({
                    'eps': eps,
                    'min_samples': min_samples,
                    'n_clusters': n_clusters,
                    'n_noise': n_noise,
                    'silhouette_score': score
                })

                print(f"eps={eps:.4f}, min_samples={min_samples}: "
                      f"聚类数={n_clusters}, 噪声点={n_noise}, 轮廓系数={score:.4f}")

                # 更新最佳参数
                if score > best_score:
                    best_score = score
                    best_eps = eps
                    best_min_samples = min_samples
                    best_n_clusters = n_clusters

        self.optimal_eps = best_eps
        self.optimal_min_samples = best_min_samples

        print(f"\n✅ DBSCAN最优参数确定：")
        print(f"- eps: {self.optimal_eps:.4f}")
        print(f"- min_samples: {self.optimal_min_samples}")
        print(f"- 预期聚类数: {best_n_clusters}")
        print(f"- 轮廓系数: {best_score:.4f}")

        # 保存参数优化结果
        dbscan_results = {
            'optimal_eps': self.optimal_eps,
            'optimal_min_samples': self.optimal_min_samples,
            'best_silhouette_score': best_score,
            'expected_clusters': best_n_clusters,
            'parameter_search_results': results
        }

        dbscan_params_file = os.path.join(self.output_dir, f"{self.timestamp}_dbscan_params.json")
        with open(dbscan_params_file, 'w', encoding='utf-8') as f:
            json.dump(dbscan_results, f, ensure_ascii=False, indent=2)

        print(f"DBSCAN参数优化结果已保存至：{dbscan_params_file}")

        return self.optimal_eps, self.optimal_min_samples
    
    def perform_kmeans_clustering(self, k=None):
        """
        执行K-Means聚类

        参数说明：
        k (int): 聚类数，如果为None则使用最优K值

        返回值：
        np.array: 聚类标签
        """
        print("\n" + "="*50)
        print("执行K-Means聚类...")

        if k is None:
            k = self.optimal_k

        if k is None:
            print("错误：未确定聚类数K")
            return None

        # 执行K-Means聚类
        # 可修改参数：K-Means算法参数
        self.kmeans_model = KMeans(n_clusters=k, random_state=42, n_init=10, max_iter=300)
        self.kmeans_labels = self.kmeans_model.fit_predict(self.scaled_data)

        # 计算最终的轮廓系数
        final_silhouette = silhouette_score(self.scaled_data, self.kmeans_labels)

        print(f"✅ K-Means聚类完成")
        print(f"聚类数：{k}")
        print(f"轮廓系数：{final_silhouette:.4f}")

        # 统计各聚类的样本数量
        unique, counts = np.unique(self.kmeans_labels, return_counts=True)
        print(f"\n各聚类样本数量：")
        for cluster_id, count in zip(unique, counts):
            percentage = (count / len(self.kmeans_labels)) * 100
            print(f"- 聚类{cluster_id}: {count}个样本 ({percentage:.1f}%)")

        return self.kmeans_labels

    def perform_dbscan_clustering(self, eps=None, min_samples=None):
        """
        执行DBSCAN聚类

        参数说明：
        eps (float): 邻域半径，如果为None则使用最优值
        min_samples (int): 最小样本数，如果为None则使用最优值

        返回值：
        np.array: 聚类标签
        """
        print("\n" + "="*50)
        print("执行DBSCAN聚类...")

        if eps is None:
            eps = self.optimal_eps
        if min_samples is None:
            min_samples = self.optimal_min_samples

        if eps is None or min_samples is None:
            print("错误：未确定DBSCAN参数")
            return None

        # 执行DBSCAN聚类
        self.dbscan_model = DBSCAN(eps=eps, min_samples=min_samples)
        self.dbscan_labels = self.dbscan_model.fit_predict(self.scaled_data)

        # 计算聚类统计信息
        n_clusters = len(set(self.dbscan_labels)) - (1 if -1 in self.dbscan_labels else 0)
        n_noise = list(self.dbscan_labels).count(-1)

        print(f"✅ DBSCAN聚类完成")
        print(f"聚类数：{n_clusters}")
        print(f"噪声点数量：{n_noise} ({n_noise/len(self.dbscan_labels)*100:.1f}%)")

        # 计算轮廓系数（排除噪声点）
        if n_clusters > 1:
            mask = self.dbscan_labels != -1
            if mask.sum() > 1:
                final_silhouette = silhouette_score(self.scaled_data[mask], self.dbscan_labels[mask])
                print(f"轮廓系数（排除噪声点）：{final_silhouette:.4f}")
            else:
                print("轮廓系数：无法计算（有效样本太少）")
        else:
            print("轮廓系数：无法计算（聚类数不足）")

        # 统计各聚类的样本数量
        unique, counts = np.unique(self.dbscan_labels, return_counts=True)
        print(f"\n各聚类样本数量：")
        for cluster_id, count in zip(unique, counts):
            percentage = (count / len(self.dbscan_labels)) * 100
            if cluster_id == -1:
                print(f"- 噪声点: {count}个样本 ({percentage:.1f}%)")
            else:
                print(f"- 聚类{cluster_id}: {count}个样本 ({percentage:.1f}%)")

        return self.dbscan_labels

    def compare_algorithms(self):
        """
        双算法对比分析
        对比K-Means和DBSCAN的聚类结果

        返回值：
        dict: 算法对比结果
        """
        print("\n" + "="*50)
        print("开始双算法对比分析...")

        if self.kmeans_labels is None or self.dbscan_labels is None:
            print("错误：需要先执行两种聚类算法")
            return None

        # 1. 聚类数量对比
        kmeans_n_clusters = len(set(self.kmeans_labels))
        dbscan_n_clusters = len(set(self.dbscan_labels)) - (1 if -1 in self.dbscan_labels else 0)
        dbscan_n_noise = list(self.dbscan_labels).count(-1)

        print(f"📊 聚类数量对比：")
        print(f"- K-Means: {kmeans_n_clusters}个聚类")
        print(f"- DBSCAN: {dbscan_n_clusters}个聚类 + {dbscan_n_noise}个噪声点")

        # 2. 轮廓系数对比
        kmeans_silhouette = silhouette_score(self.scaled_data, self.kmeans_labels)

        # DBSCAN轮廓系数（排除噪声点）
        if dbscan_n_clusters > 1:
            mask = self.dbscan_labels != -1
            if mask.sum() > 1:
                dbscan_silhouette = silhouette_score(self.scaled_data[mask], self.dbscan_labels[mask])
            else:
                dbscan_silhouette = -1
        else:
            dbscan_silhouette = -1

        print(f"📊 轮廓系数对比：")
        print(f"- K-Means: {kmeans_silhouette:.4f}")
        print(f"- DBSCAN: {dbscan_silhouette:.4f}")

        # 3. 聚类稳定性分析（通过多次运行评估）
        print(f"📊 聚类稳定性分析...")
        kmeans_stability = self._evaluate_clustering_stability('kmeans')
        dbscan_stability = self._evaluate_clustering_stability('dbscan')

        print(f"- K-Means稳定性: {kmeans_stability:.4f}")
        print(f"- DBSCAN稳定性: {dbscan_stability:.4f}")

        # 4. 业务解释性对比
        kmeans_business_score = self._evaluate_business_interpretability(self.kmeans_labels, 'K-Means')
        dbscan_business_score = self._evaluate_business_interpretability(self.dbscan_labels, 'DBSCAN')

        # 5. 综合评分
        kmeans_total_score = (kmeans_silhouette * 0.4 + kmeans_stability * 0.3 +
                             kmeans_business_score * 0.3)
        dbscan_total_score = (dbscan_silhouette * 0.4 + dbscan_stability * 0.3 +
                             dbscan_business_score * 0.3)

        # 保存对比结果
        self.algorithm_comparison = {
            'kmeans': {
                'n_clusters': kmeans_n_clusters,
                'silhouette_score': kmeans_silhouette,
                'stability_score': kmeans_stability,
                'business_score': kmeans_business_score,
                'total_score': kmeans_total_score
            },
            'dbscan': {
                'n_clusters': dbscan_n_clusters,
                'n_noise': dbscan_n_noise,
                'silhouette_score': dbscan_silhouette,
                'stability_score': dbscan_stability,
                'business_score': dbscan_business_score,
                'total_score': dbscan_total_score
            },
            'recommendation': 'K-Means' if kmeans_total_score > dbscan_total_score else 'DBSCAN'
        }

        print(f"\n📊 综合评分对比：")
        print(f"- K-Means总分: {kmeans_total_score:.4f}")
        print(f"- DBSCAN总分: {dbscan_total_score:.4f}")
        print(f"- 推荐算法: {self.algorithm_comparison['recommendation']}")

        # 保存对比结果
        comparison_file = os.path.join(self.output_dir, f"{self.timestamp}_algorithm_comparison.json")
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(self.algorithm_comparison, f, ensure_ascii=False, indent=2)

        print(f"✅ 算法对比分析完成，结果已保存至：{comparison_file}")

        return self.algorithm_comparison

    def _evaluate_clustering_stability(self, algorithm, n_runs=5):
        """
        评估聚类稳定性
        通过多次运行计算结果的一致性

        参数说明：
        algorithm (str): 算法类型 ('kmeans' 或 'dbscan')
        n_runs (int): 运行次数

        返回值：
        float: 稳定性分数
        """
        stability_scores = []

        for i in range(n_runs):
            if algorithm == 'kmeans':
                # 使用不同随机种子
                model = KMeans(n_clusters=self.optimal_k, random_state=42+i, n_init=10)
                labels = model.fit_predict(self.scaled_data)
                reference_labels = self.kmeans_labels
            else:  # dbscan
                # DBSCAN是确定性的，通过添加小噪声评估稳定性
                noise = np.random.normal(0, 0.01, self.scaled_data.shape)
                noisy_data = self.scaled_data + noise
                model = DBSCAN(eps=self.optimal_eps, min_samples=self.optimal_min_samples)
                labels = model.fit_predict(noisy_data)
                reference_labels = self.dbscan_labels

            # 计算与原始结果的一致性（使用调整兰德指数）
            from sklearn.metrics import adjusted_rand_score
            consistency = adjusted_rand_score(reference_labels, labels)
            stability_scores.append(max(0, consistency))  # 确保非负

        return np.mean(stability_scores)

    def _evaluate_business_interpretability(self, labels, algorithm_name):
        """
        评估业务解释性
        基于聚类结果的业务意义评分

        参数说明：
        labels (np.array): 聚类标签
        algorithm_name (str): 算法名称

        返回值：
        float: 业务解释性分数
        """
        print(f"📊 {algorithm_name}业务解释性分析...")

        # 计算目标变量在各聚类中的分布差异
        target_col = '是否购买理财'
        if target_col not in self.data.columns:
            return 0.5  # 无目标变量时返回中等分数

        cluster_target_ratios = []
        unique_labels = set(labels)
        if -1 in unique_labels:
            unique_labels.remove(-1)  # 排除噪声点

        for cluster_id in unique_labels:
            cluster_mask = labels == cluster_id
            cluster_data = self.data[cluster_mask]

            if len(cluster_data) > 0:
                # 计算正样本比例
                positive_mask = (cluster_data[target_col] == 1) | (cluster_data[target_col] == '1')
                positive_ratio = positive_mask.sum() / len(cluster_data)
                cluster_target_ratios.append(positive_ratio)

        if len(cluster_target_ratios) == 0:
            return 0.0

        # 计算聚类间目标变量分布的差异程度
        # 差异越大，业务解释性越好
        ratio_std = np.std(cluster_target_ratios)
        ratio_range = max(cluster_target_ratios) - min(cluster_target_ratios)

        # 归一化分数（0-1之间）
        business_score = min(1.0, ratio_range * 2)  # 范围越大分数越高

        print(f"- 各聚类正样本比例: {[f'{r:.3f}' for r in cluster_target_ratios]}")
        print(f"- 比例标准差: {ratio_std:.3f}")
        print(f"- 比例范围: {ratio_range:.3f}")
        print(f"- 业务解释性分数: {business_score:.3f}")

        return business_score
    
    def analyze_clusters(self):
        """
        聚类结果分析
        分析各聚类的特征中心和客户画像
        """
        print("\n" + "="*50)
        print("开始聚类结果分析...")
        
        if self.cluster_labels is None:
            print("错误：尚未执行聚类")
            return None
        
        # 将聚类标签添加到原始数据
        clustered_data = self.data.copy()
        clustered_data['聚类标签'] = self.cluster_labels
        
        # 分析各聚类的特征中心
        cluster_centers = []
        target_col = '是否购买理财'
        
        # 获取所有聚类ID（包括DBSCAN的噪声点-1）
        unique_clusters = sorted(set(self.cluster_labels))

        for cluster_id in unique_clusters:
            cluster_data = clustered_data[clustered_data['聚类标签'] == cluster_id]
            
            # 计算特征均值
            feature_means = cluster_data[self.features].mean()
            
            # 计算正样本比例（如果目标变量存在）
            if target_col in clustered_data.columns:
                # 处理目标变量的不同值类型
                positive_mask = (cluster_data[target_col] == 1) | (cluster_data[target_col] == '1')
                positive_ratio = positive_mask.sum() / len(cluster_data)
            else:
                positive_ratio = 0
            
            cluster_info = {
                '聚类ID': cluster_id,
                '样本数量': len(cluster_data),
                '样本比例(%)': round((len(cluster_data) / len(clustered_data)) * 100, 2),
                '正样本比例(%)': round(positive_ratio * 100, 2),
                '特征均值': feature_means.to_dict()
            }
            
            cluster_centers.append(cluster_info)
            
            print(f"\n聚类{cluster_id}分析：")
            print(f"- 样本数量：{len(cluster_data)}")
            print(f"- 样本比例：{cluster_info['样本比例(%)']}%")
            print(f"- 正样本比例：{cluster_info['正样本比例(%)']}%")
        
        # 保存聚类分析结果
        cluster_analysis_file = os.path.join(self.output_dir, f"{self.timestamp}_cluster_analysis.json")
        with open(cluster_analysis_file, 'w', encoding='utf-8') as f:
            json.dump(cluster_centers, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 聚类分析完成，结果已保存至：{cluster_analysis_file}")
        
        return cluster_centers, clustered_data

    def visualize_clusters(self, clustered_data):
        """
        聚类可视化
        使用PCA降维进行聚类结果可视化

        参数说明：
        clustered_data (pd.DataFrame): 包含聚类标签的数据
        """
        print("\n" + "="*50)
        print("生成聚类可视化图表...")

        # PCA降维到2D
        pca = PCA(n_components=2, random_state=42)
        pca_data = pca.fit_transform(self.scaled_data)

        # 创建可视化图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. PCA散点图
        scatter = axes[0, 0].scatter(pca_data[:, 0], pca_data[:, 1],
                                   c=self.cluster_labels, cmap='viridis', alpha=0.6)
        axes[0, 0].set_xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.1%})')
        axes[0, 0].set_ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.1%})')
        axes[0, 0].set_title('PCA降维聚类散点图', fontsize=14, fontweight='bold')
        plt.colorbar(scatter, ax=axes[0, 0], label='聚类标签')

        # 2. 聚类规模对比
        cluster_counts = pd.Series(self.cluster_labels).value_counts().sort_index()
        axes[0, 1].bar(cluster_counts.index, cluster_counts.values, color='skyblue', alpha=0.7)
        axes[0, 1].set_xlabel('聚类标签')
        axes[0, 1].set_ylabel('样本数量')
        axes[0, 1].set_title('各聚类规模对比', fontsize=14, fontweight='bold')
        axes[0, 1].grid(True, alpha=0.3)

        # 在柱状图上添加数值标签
        for i, v in enumerate(cluster_counts.values):
            axes[0, 1].text(i, v + max(cluster_counts.values) * 0.01, str(v),
                           ha='center', va='bottom', fontweight='bold')

        # 3. 聚类规模饼图
        axes[1, 0].pie(cluster_counts.values, labels=[f'聚类{i}' for i in cluster_counts.index],
                      autopct='%1.1f%%', startangle=90)
        axes[1, 0].set_title('聚类规模分布', fontsize=14, fontweight='bold')

        # 4. 正样本比例对比（如果有目标变量）
        target_col = '是否购买理财'
        if target_col in clustered_data.columns:
            positive_ratios = []
            cluster_ids = []

            # 获取所有聚类ID（包括DBSCAN的噪声点-1）
            unique_clusters = sorted(set(self.cluster_labels))

            for cluster_id in unique_clusters:
                cluster_data = clustered_data[clustered_data['聚类标签'] == cluster_id]
                if len(cluster_data) > 0:
                    positive_mask = (cluster_data[target_col] == 1) | (cluster_data[target_col] == '1')
                    positive_ratio = positive_mask.sum() / len(cluster_data) * 100
                    positive_ratios.append(positive_ratio)
                    cluster_ids.append(cluster_id)

            if positive_ratios:
                bars = axes[1, 1].bar(range(len(cluster_ids)), positive_ratios, color='lightcoral', alpha=0.7)
                axes[1, 1].set_xlabel('聚类标签')
                axes[1, 1].set_ylabel('正样本比例 (%)')
                axes[1, 1].set_title('各聚类正样本比例', fontsize=14, fontweight='bold')
                axes[1, 1].grid(True, alpha=0.3)

                # 设置x轴标签
                axes[1, 1].set_xticks(range(len(cluster_ids)))
                axes[1, 1].set_xticklabels([f'聚类{cid}' if cid != -1 else '噪声' for cid in cluster_ids])

                # 添加数值标签
                for i, v in enumerate(positive_ratios):
                    axes[1, 1].text(i, v + max(positive_ratios) * 0.01, f'{v:.1f}%',
                                   ha='center', va='bottom', fontweight='bold')
        else:
            axes[1, 1].text(0.5, 0.5, '无目标变量数据', ha='center', va='center',
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].set_title('正样本比例分析', fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存图表
        cluster_plot = os.path.join(self.output_dir, f"{self.timestamp}_cluster_visualization.png")
        plt.savefig(cluster_plot, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 聚类可视化图表已保存至：{cluster_plot}")

        return cluster_plot

    def generate_customer_profiles(self, cluster_centers, clustered_data):
        """
        生成客户画像
        为每个聚类生成详细的客户画像描述

        参数说明：
        cluster_centers (list): 聚类中心信息
        clustered_data (pd.DataFrame): 包含聚类标签的数据

        返回值：
        dict: 客户画像字典
        """
        print("\n" + "="*50)
        print("生成客户画像...")

        customer_profiles = {}

        # 计算全体客户的特征均值作为基准
        overall_means = clustered_data[self.features].mean()

        for cluster_info in cluster_centers:
            cluster_id = cluster_info['聚类ID']
            feature_means = cluster_info['特征均值']

            # 分析特征相对于全体均值的表现
            profile_description = []
            high_features = []  # 高于平均的特征
            low_features = []   # 低于平均的特征

            for feature, value in feature_means.items():
                overall_mean = overall_means[feature]
                ratio = value / overall_mean if overall_mean != 0 else 1

                if ratio > 1.2:  # 高于平均20%
                    high_features.append((feature, ratio))
                elif ratio < 0.8:  # 低于平均20%
                    low_features.append((feature, ratio))

            # 排序特征
            high_features.sort(key=lambda x: x[1], reverse=True)
            low_features.sort(key=lambda x: x[1])

            # 生成画像描述
            profile_description.append(f"聚类{cluster_id}客户画像：")
            profile_description.append(f"- 客户数量：{cluster_info['样本数量']}个 ({cluster_info['样本比例(%)']}%)")
            profile_description.append(f"- 购买理财比例：{cluster_info['正样本比例(%)']}%")

            if high_features:
                profile_description.append("\n突出特征（高于平均）：")
                for feature, ratio in high_features[:5]:  # 显示前5个
                    profile_description.append(f"  • {feature}: {ratio:.1f}倍")

            if low_features:
                profile_description.append("\n相对较低特征：")
                for feature, ratio in low_features[:5]:  # 显示前5个
                    profile_description.append(f"  • {feature}: {ratio:.1f}倍")

            # 根据特征组合生成业务标签
            business_label = self._generate_business_label(cluster_info, high_features, low_features)
            profile_description.append(f"\n业务标签：{business_label}")

            customer_profiles[cluster_id] = {
                'description': '\n'.join(profile_description),
                'high_features': high_features,
                'low_features': low_features,
                'business_label': business_label,
                'cluster_info': cluster_info
            }

            print(f"\n{profile_description[0]}")
            print(f"业务标签：{business_label}")

        # 保存客户画像
        profiles_file = os.path.join(self.output_dir, f"{self.timestamp}_customer_profiles.json")
        with open(profiles_file, 'w', encoding='utf-8') as f:
            json.dump(customer_profiles, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 客户画像生成完成，结果已保存至：{profiles_file}")

        return customer_profiles

    def _generate_business_label(self, cluster_info, high_features, low_features):
        """
        根据聚类特征生成业务标签

        参数说明：
        cluster_info (dict): 聚类信息
        high_features (list): 高于平均的特征
        low_features (list): 低于平均的特征

        返回值：
        str: 业务标签
        """
        positive_ratio = cluster_info['正样本比例(%)']

        # 根据购买理财比例确定基础标签
        if positive_ratio > 5:
            base_label = "高价值客户"
        elif positive_ratio > 1:
            base_label = "潜力客户"
        else:
            base_label = "普通客户"

        # 根据主要特征添加描述
        feature_descriptions = []

        for feature, ratio in high_features[:3]:  # 取前3个最突出的特征
            if '存款' in feature:
                feature_descriptions.append("高存款")
            elif '交易' in feature:
                feature_descriptions.append("高活跃")
            elif '网银' in feature:
                feature_descriptions.append("数字化")
            elif 'EVA' in feature:
                feature_descriptions.append("高贡献")
            elif '代发' in feature:
                feature_descriptions.append("代发工资")

        # 组合标签
        if feature_descriptions:
            business_label = f"{base_label} - {'/'.join(feature_descriptions[:2])}"
        else:
            business_label = base_label

        return business_label

    def identify_potential_customers(self, clustered_data, customer_profiles):
        """
        识别高潜力客户
        基于聚类结果和客户画像识别高潜力客户

        参数说明：
        clustered_data (pd.DataFrame): 包含聚类标签的数据
        customer_profiles (dict): 客户画像字典

        返回值：
        str: 高潜力客户名单文件路径
        """
        print("\n" + "="*50)
        print("识别高潜力客户...")

        # 根据正样本比例排序聚类
        cluster_potential = []
        for cluster_id, profile in customer_profiles.items():
            positive_ratio = profile['cluster_info']['正样本比例(%)']
            cluster_potential.append((cluster_id, positive_ratio))

        cluster_potential.sort(key=lambda x: x[1], reverse=True)

        # 选择前几个高潜力聚类
        high_potential_clusters = [cluster_id for cluster_id, ratio in cluster_potential if ratio > 1.0]

        if not high_potential_clusters:
            # 如果没有明显的高潜力聚类，选择比例最高的
            high_potential_clusters = [cluster_potential[0][0]]

        print(f"识别出{len(high_potential_clusters)}个高潜力聚类：")
        for cluster_id in high_potential_clusters:
            ratio = customer_profiles[cluster_id]['cluster_info']['正样本比例(%)']
            label = customer_profiles[cluster_id]['business_label']
            print(f"- 聚类{cluster_id}: {label} (正样本比例: {ratio:.2f}%)")

        # 提取高潜力客户
        potential_customers = clustered_data[
            clustered_data['聚类标签'].isin(high_potential_clusters)
        ].copy()

        # 添加潜力评分（基于聚类的正样本比例）
        potential_customers['潜力评分'] = potential_customers['聚类标签'].map(
            {cluster_id: customer_profiles[cluster_id]['cluster_info']['正样本比例(%)']
             for cluster_id in customer_profiles.keys()}
        )

        # 添加业务标签
        potential_customers['业务标签'] = potential_customers['聚类标签'].map(
            {cluster_id: customer_profiles[cluster_id]['business_label']
             for cluster_id in customer_profiles.keys()}
        )

        # 选择输出列
        output_cols = ['客户号', '聚类标签', '业务标签', '潜力评分'] + self.features[:10]  # 只保留前10个特征
        potential_list = potential_customers[output_cols].copy()

        # 按潜力评分排序
        potential_list = potential_list.sort_values('潜力评分', ascending=False)

        # 保存高潜力客户名单
        potential_file = os.path.join(self.output_dir, f"{self.timestamp}_potential_list_from_clustering.csv")
        potential_list.to_csv(potential_file, index=False, encoding='utf-8-sig')

        print(f"✅ 高潜力客户识别完成")
        print(f"高潜力客户数量：{len(potential_list)}")
        print(f"结果已保存至：{potential_file}")

        return potential_file

    def save_clustering_results(self, clustered_data):
        """
        保存聚类结果

        参数说明：
        clustered_data (pd.DataFrame): 包含聚类标签的数据

        返回值：
        str: 聚类结果文件路径
        """
        print("\n" + "="*50)
        print("保存聚类结果...")

        # 保存完整的聚类结果
        clustering_file = os.path.join(self.output_dir, f"{self.timestamp}_clustering_results.csv")
        clustered_data.to_csv(clustering_file, index=False, encoding='utf-8-sig')

        print(f"✅ 聚类结果已保存至：{clustering_file}")

        return clustering_file

    def generate_clustering_report(self, customer_profiles):
        """
        生成聚类分析报告

        参数说明：
        customer_profiles (dict): 客户画像字典

        返回值：
        str: 报告文件路径
        """
        print("\n" + "="*50)
        print("生成聚类分析报告...")

        report_file = os.path.join(self.output_dir, f"{self.timestamp}_clustering_report.md")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 客户聚类分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            # 聚类概述
            f.write("## 1. 聚类概述\n\n")
            f.write(f"- **聚类方法：** K-Means\n")
            f.write(f"- **最优聚类数：** {self.optimal_k}\n")
            f.write(f"- **使用特征数：** {len(self.features)}\n")
            f.write(f"- **数据标准化：** StandardScaler\n")
            f.write(f"- **总样本数：** {len(self.data):,}\n\n")

            # 聚类结果
            f.write("## 2. 聚类结果\n\n")
            for cluster_id in sorted(customer_profiles.keys()):
                profile = customer_profiles[cluster_id]
                cluster_info = profile['cluster_info']

                f.write(f"### 聚类{cluster_id}：{profile['business_label']}\n\n")
                f.write(f"- **样本数量：** {cluster_info['样本数量']:,}个 ({cluster_info['样本比例(%)']}%)\n")
                f.write(f"- **购买理财比例：** {cluster_info['正样本比例(%)']}%\n")

                # 主要特征
                if profile['high_features']:
                    f.write(f"- **突出特征：** ")
                    top_features = [f"{feat}({ratio:.1f}倍)" for feat, ratio in profile['high_features'][:3]]
                    f.write(", ".join(top_features))
                    f.write("\n")

                f.write("\n")

            # 业务洞察
            f.write("## 3. 业务洞察\n\n")

            # 按购买理财比例排序聚类
            sorted_clusters = sorted(customer_profiles.items(),
                                   key=lambda x: x[1]['cluster_info']['正样本比例(%)'],
                                   reverse=True)

            f.write("### 客户价值排序\n\n")
            for i, (cluster_id, profile) in enumerate(sorted_clusters):
                ratio = profile['cluster_info']['正样本比例(%)']
                label = profile['business_label']
                f.write(f"{i+1}. **聚类{cluster_id}** - {label} (购买比例: {ratio:.2f}%)\n")
            f.write("\n")

            # 营销建议
            f.write("### 营销策略建议\n\n")

            high_value_clusters = [cluster_id for cluster_id, profile in customer_profiles.items()
                                 if profile['cluster_info']['正样本比例(%)'] > 2.0]

            if high_value_clusters:
                f.write("**高价值客户群体：**\n")
                for cluster_id in high_value_clusters:
                    profile = customer_profiles[cluster_id]
                    f.write(f"- 聚类{cluster_id}：重点营销，提供个性化理财产品\n")
                f.write("\n")

            medium_value_clusters = [cluster_id for cluster_id, profile in customer_profiles.items()
                                   if 0.5 <= profile['cluster_info']['正样本比例(%)'] <= 2.0]

            if medium_value_clusters:
                f.write("**潜力客户群体：**\n")
                for cluster_id in medium_value_clusters:
                    profile = customer_profiles[cluster_id]
                    f.write(f"- 聚类{cluster_id}：定期跟进，教育培养\n")
                f.write("\n")

            # 特征重要性
            f.write("## 4. 关键特征分析\n\n")
            f.write("基于聚类分析，以下特征对客户分群最为重要：\n\n")
            for i, feature in enumerate(self.features[:10]):
                f.write(f"{i+1}. {feature}\n")
            f.write("\n")

            # 应用建议
            f.write("## 5. 应用建议\n\n")
            f.write("1. **精准营销：** 根据客户聚类制定差异化营销策略\n")
            f.write("2. **产品设计：** 基于客户画像设计针对性理财产品\n")
            f.write("3. **资源配置：** 优先投入资源到高价值客户群体\n")
            f.write("4. **客户维护：** 建立分层的客户服务体系\n")
            f.write("5. **效果监控：** 定期评估聚类效果和营销转化率\n\n")

            f.write("---\n\n")
            f.write("*本报告由客户聚类分析模块自动生成*\n")

        print(f"聚类分析报告已保存至：{report_file}")
        return report_file

    def run_complete_clustering(self, algorithm_choice='both'):
        """
        运行完整的客户聚类分析流程（双算法增强版）

        参数说明：
        algorithm_choice (str): 算法选择 ('kmeans', 'dbscan', 'both')

        返回值：
        bool: 聚类分析是否成功完成
        """
        try:
            print("开始执行完整的客户聚类分析流程...")
            print("="*60)

            # 模块5增强：用户可选择执行K-Means、DBSCAN或两者都执行
            print(f"\n🔧 聚类算法选择")
            print("="*50)
            print("请选择要执行的聚类算法：")
            print("1. kmeans - K-Means聚类（经典算法）")
            print("2. dbscan - DBSCAN聚类（密度聚类）")
            print("3. both - 两种算法都执行（推荐）")

            while True:
                choice = input("\n请选择算法 (1-3，默认为3): ").strip()
                if choice == '' or choice == '3':
                    algorithm_choice = 'both'
                    break
                elif choice == '1':
                    algorithm_choice = 'kmeans'
                    break
                elif choice == '2':
                    algorithm_choice = 'dbscan'
                    break
                else:
                    print("⚠️ 请输入有效选项 (1-3)")

            print(f"✅ 选择的算法：{algorithm_choice}")

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 数据标准化（使用全量数据）
            self.standardize_data(use_full_data=True)

            # 3. 执行选择的算法
            if algorithm_choice in ['kmeans', 'both']:
                print(f"\n🔄 执行K-Means聚类...")
                # 确定最优K值
                self.determine_optimal_k()
                # 执行K-Means聚类
                self.perform_kmeans_clustering()

            if algorithm_choice in ['dbscan', 'both']:
                print(f"\n🔄 执行DBSCAN聚类...")
                # 确定DBSCAN最优参数
                self.determine_optimal_dbscan_params()
                # 执行DBSCAN聚类
                self.perform_dbscan_clustering()

            # 4. 算法对比分析（如果执行了两种算法）
            if algorithm_choice == 'both':
                self.compare_algorithms()

            # 5. 聚类结果分析（使用推荐的算法或用户选择的算法）
            if algorithm_choice == 'both':
                # 使用推荐的算法进行后续分析
                recommended_algorithm = self.algorithm_comparison['recommendation']
                if recommended_algorithm == 'K-Means':
                    cluster_labels = self.kmeans_labels
                    print(f"\n📊 使用推荐算法 K-Means 进行详细分析...")
                else:
                    cluster_labels = self.dbscan_labels
                    print(f"\n📊 使用推荐算法 DBSCAN 进行详细分析...")
            elif algorithm_choice == 'kmeans':
                cluster_labels = self.kmeans_labels
            else:  # dbscan
                cluster_labels = self.dbscan_labels

            # 临时设置cluster_labels用于兼容现有方法
            self.cluster_labels = cluster_labels

            cluster_centers, clustered_data = self.analyze_clusters()

            # 6. 聚类可视化
            self.visualize_clusters(clustered_data)

            # 7. 生成客户画像
            customer_profiles = self.generate_customer_profiles(cluster_centers, clustered_data)

            # 8. 识别高潜力客户
            self.identify_potential_customers(clustered_data, customer_profiles)

            # 9. 保存聚类结果
            self.save_clustering_results(clustered_data)

            # 10. 生成聚类报告
            self.generate_clustering_report(customer_profiles)

            print("\n" + "="*60)
            print("✅ 客户聚类分析完成！")
            print(f"所有结果已保存至：{self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 聚类分析过程中出现错误：{str(e)}")
            return False


def main():
    """
    主函数：执行客户聚类分析

    使用说明：
    1. 确保预处理数据和特征列表文件存在
    2. 运行脚本自动确定最优聚类数并执行聚类
    3. 查看生成的聚类结果和客户画像

    迁移到其他项目时需要修改的部分：
    - data_path: 修改为实际的预处理数据文件路径
    - features_path: 修改为实际的特征列表文件路径
    - output_dir: 修改为期望的输出目录
    - 根据业务需求调整聚类参数和客户画像生成逻辑
    """

    # 配置参数（迁移时需要修改这些路径）
    data_path = "output4/02_processing/processed_data.csv"  # 预处理数据路径
    features_path = "output4/03_feature_selection/final_features.json"  # 特征列表路径
    output_dir = "output4/05_clustering"  # 输出目录

    # 创建客户聚类分析器实例
    clustering = CustomerClustering(data_path, features_path, output_dir)

    # 执行完整聚类分析
    success = clustering.run_complete_clustering()

    if success:
        print("\n🎉 客户聚类分析成功完成！")
        print("📊 请查看生成的聚类结果和客户画像")
        print(f"📁 聚类结果文件：{output_dir}/clustering_results.csv")
        print(f"🎯 高潜力客户名单：{output_dir}/potential_list_from_clustering.csv")
    else:
        print("\n❌ 客户聚类分析失败，请检查错误信息")


if __name__ == "__main__":
    main()
