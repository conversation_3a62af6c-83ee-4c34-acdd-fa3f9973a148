#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块3：PU学习与特征选择
功能：负样本生成、正负样本分析、特征选择
作者：AI助手
创建时间：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import json
from datetime import datetime
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import lightgbm as lgb
import shap
from sklearn.inspection import permutation_importance
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PULearningFeatureSelector:
    """
    PU学习与特征选择类
    用于负样本生成、正负样本分析和特征选择
    """
    
    def __init__(self, data_path, output_dir):
        """
        初始化PU学习特征选择器
        
        参数说明：
        data_path (str): 预处理后的数据文件路径
        output_dir (str): 输出目录路径
        """
        self.data_path = data_path
        self.output_dir = output_dir
        self.data = None
        self.labeled_data = None  # 生成标签后的数据
        self.final_features = None  # 最终选择的特征
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """
        加载预处理后的数据
        """
        try:
            print("正在加载预处理后的数据...")
            self.data = pd.read_csv(self.data_path, encoding='utf-8-sig')
            print(f"数据加载成功！数据形状：{self.data.shape}")
            
            # 检查目标变量
            target_col = '是否购买理财'
            if target_col not in self.data.columns:
                print(f"错误：未找到目标变量列'{target_col}'")
                return False
            
            # 统计目标变量分布
            target_counts = self.data[target_col].value_counts()
            print(f"目标变量分布：")
            for value, count in target_counts.items():
                print(f"- {value}: {count}个")
            
            return True
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def generate_negative_samples(self, method='spy'):
        """
        生成负样本
        
        参数说明：
        method (str): 负样本生成方法
                     'spy' - Spy Technique
                     'isolation' - Isolation Forest
                     'ocsvm' - One-Class SVM
        """
        print("\n" + "="*50)
        print(f"开始使用{method}方法生成负样本...")
        
        target_col = '是否购买理财'
        
        # 分离正样本和未知样本
        # 注意：目标变量可能是字符串'1'或整数1
        positive_samples = self.data[
            (self.data[target_col] == 1) | (self.data[target_col] == '1')
        ].copy()
        unknown_samples = self.data[self.data[target_col] == '未知'].copy()
        
        print(f"正样本数量：{len(positive_samples)}")
        print(f"未知样本数量：{len(unknown_samples)}")
        
        # 获取特征列（排除目标变量和ID列）
        feature_cols = [col for col in self.data.columns 
                       if col not in [target_col, '客户号']]
        
        if method == 'spy':
            negative_samples = self._spy_technique(positive_samples, unknown_samples, feature_cols)
        elif method == 'isolation':
            negative_samples = self._isolation_forest_method(positive_samples, unknown_samples, feature_cols)
        elif method == 'ocsvm':
            negative_samples = self._one_class_svm_method(positive_samples, unknown_samples, feature_cols)
        else:
            print(f"不支持的方法：{method}")
            return False
        
        # 合并正负样本
        positive_samples[target_col] = 1
        negative_samples[target_col] = 0
        
        self.labeled_data = pd.concat([positive_samples, negative_samples], ignore_index=True)
        
        print(f"✅ 负样本生成完成")
        print(f"最终标注数据：正样本{len(positive_samples)}个，负样本{len(negative_samples)}个")
        
        # 保存标注后的数据
        labeled_file = os.path.join(self.output_dir, "labeled_samples.csv")
        self.labeled_data.to_csv(labeled_file, index=False, encoding='utf-8-sig')
        print(f"标注数据已保存至：{labeled_file}")
        
        return True
    
    def _spy_technique(self, positive_samples, unknown_samples, feature_cols):
        """
        Spy Technique方法生成负样本
        """
        print("使用Spy Technique方法...")
        
        # 从正样本中随机选择15%作为"间谍"
        spy_ratio = 0.15
        spy_samples = positive_samples.sample(frac=spy_ratio, random_state=42)
        remaining_positive = positive_samples.drop(spy_samples.index)
        
        print(f"间谍样本数量：{len(spy_samples)}")
        
        # 将间谍样本混入未知样本
        spy_unknown = pd.concat([unknown_samples, spy_samples], ignore_index=True)
        spy_unknown['is_spy'] = 0
        spy_unknown.loc[spy_unknown.index >= len(unknown_samples), 'is_spy'] = 1
        
        # 训练分类器区分正样本和混合样本
        X_pos = remaining_positive[feature_cols]
        X_spy_unknown = spy_unknown[feature_cols]
        y_pos = np.ones(len(X_pos))
        y_spy_unknown = np.zeros(len(X_spy_unknown))
        
        X_train = pd.concat([X_pos, X_spy_unknown])
        y_train = np.concatenate([y_pos, y_spy_unknown])
        
        # 使用LightGBM训练分类器
        train_data = lgb.Dataset(X_train, label=y_train)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'verbose': -1
        }
        
        model = lgb.train(params, train_data, num_boost_round=100)
        
        # 预测间谍样本的分数
        spy_scores = model.predict(spy_samples[feature_cols])
        threshold = np.percentile(spy_scores, 10)  # 使用10%分位数作为阈值
        
        print(f"间谍样本分数阈值：{threshold:.4f}")
        
        # 识别负样本（分数低于阈值的未知样本）
        unknown_scores = model.predict(unknown_samples[feature_cols])
        negative_mask = unknown_scores < threshold
        negative_samples = unknown_samples[negative_mask].copy()
        
        print(f"识别出{len(negative_samples)}个负样本")
        
        return negative_samples
    
    def _isolation_forest_method(self, positive_samples, unknown_samples, feature_cols):
        """
        Isolation Forest方法生成负样本
        """
        print("使用Isolation Forest方法...")
        
        # 使用正样本训练Isolation Forest
        X_pos = positive_samples[feature_cols]
        
        # 标准化特征
        scaler = StandardScaler()
        X_pos_scaled = scaler.fit_transform(X_pos)
        
        # 训练Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        iso_forest.fit(X_pos_scaled)
        
        # 预测未知样本
        X_unknown = unknown_samples[feature_cols]
        X_unknown_scaled = scaler.transform(X_unknown)
        
        # 获取异常分数（负值表示异常）
        anomaly_scores = iso_forest.decision_function(X_unknown_scaled)
        predictions = iso_forest.predict(X_unknown_scaled)
        
        # 选择异常样本作为负样本
        negative_mask = predictions == -1
        negative_samples = unknown_samples[negative_mask].copy()
        
        print(f"识别出{len(negative_samples)}个负样本")
        
        return negative_samples
    
    def _one_class_svm_method(self, positive_samples, unknown_samples, feature_cols):
        """
        One-Class SVM方法生成负样本
        """
        print("使用One-Class SVM方法...")
        
        # 使用正样本训练One-Class SVM
        X_pos = positive_samples[feature_cols]
        
        # 标准化特征
        scaler = StandardScaler()
        X_pos_scaled = scaler.fit_transform(X_pos)
        
        # 训练One-Class SVM
        oc_svm = OneClassSVM(nu=0.1, kernel='rbf', gamma='scale')
        oc_svm.fit(X_pos_scaled)
        
        # 预测未知样本
        X_unknown = unknown_samples[feature_cols]
        X_unknown_scaled = scaler.transform(X_unknown)
        
        predictions = oc_svm.predict(X_unknown_scaled)
        
        # 选择异常样本作为负样本
        negative_mask = predictions == -1
        negative_samples = unknown_samples[negative_mask].copy()
        
        print(f"识别出{len(negative_samples)}个负样本")
        
        return negative_samples
    
    def analyze_positive_negative_samples(self):
        """
        正负样本对比分析
        """
        print("\n" + "="*50)
        print("开始正负样本对比分析...")
        
        if self.labeled_data is None:
            print("错误：尚未生成标注数据")
            return False
        
        target_col = '是否购买理财'
        feature_cols = [col for col in self.labeled_data.columns 
                       if col not in [target_col, '客户号']]
        
        # 分离正负样本
        positive_data = self.labeled_data[self.labeled_data[target_col] == 1]
        negative_data = self.labeled_data[self.labeled_data[target_col] == 0]
        
        print(f"正样本数量：{len(positive_data)}")
        print(f"负样本数量：{len(negative_data)}")
        
        # 选择重要特征进行可视化（选择前12个数值型特征）
        numerical_features = self.labeled_data[feature_cols].select_dtypes(include=[np.number]).columns.tolist()
        plot_features = numerical_features[:12]
        
        # 创建对比图
        n_cols = 3
        n_rows = (len(plot_features) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature in enumerate(plot_features):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col]
            
            # 绘制分布对比
            positive_data[feature].hist(bins=30, alpha=0.7, label='正样本', 
                                      color='lightblue', ax=ax, density=True)
            negative_data[feature].hist(bins=30, alpha=0.7, label='负样本', 
                                      color='lightcoral', ax=ax, density=True)
            
            ax.set_title(f'{feature}分布对比', fontsize=10, fontweight='bold')
            ax.set_xlabel(feature, fontsize=9)
            ax.set_ylabel('密度', fontsize=9)
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(len(plot_features), n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图表
        comparison_plot = os.path.join(self.output_dir, f"{self.timestamp}_positive_negative_comparison.png")
        plt.savefig(comparison_plot, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 正负样本对比分析完成，图表已保存至：{comparison_plot}")
        
        return True

    def calculate_iv(self, feature, target, bins=10):
        """
        计算特征的信息价值(Information Value)

        参数说明：
        feature (pd.Series): 特征数据
        target (pd.Series): 目标变量
        bins (int): 分箱数量

        返回值：
        float: IV值
        """
        # 对连续变量进行分箱
        if feature.dtype in ['float64', 'int64']:
            feature_binned = pd.cut(feature, bins=bins, duplicates='drop')
        else:
            feature_binned = feature

        # 计算每个分箱的正负样本比例
        crosstab = pd.crosstab(feature_binned, target)

        if crosstab.shape[1] < 2:
            return 0

        # 计算WOE和IV
        total_pos = crosstab.iloc[:, 1].sum()
        total_neg = crosstab.iloc[:, 0].sum()

        if total_pos == 0 or total_neg == 0:
            return 0

        iv = 0
        for i in range(len(crosstab)):
            pos_rate = crosstab.iloc[i, 1] / total_pos if total_pos > 0 else 0
            neg_rate = crosstab.iloc[i, 0] / total_neg if total_neg > 0 else 0

            if pos_rate > 0 and neg_rate > 0:
                woe = np.log(pos_rate / neg_rate)
                iv += (pos_rate - neg_rate) * woe

        return iv

    def feature_selection_iv(self, threshold=0.02):
        """
        基于IV值进行特征粗筛

        参数说明：
        threshold (float): IV值阈值，低于此值的特征将被剔除

        返回值：
        list: 通过IV筛选的特征列表
        """
        print("\n" + "="*50)
        print("开始基于IV值的特征粗筛...")

        if self.labeled_data is None:
            print("错误：尚未生成标注数据")
            return []

        target_col = '是否购买理财'
        feature_cols = [col for col in self.labeled_data.columns
                       if col not in [target_col, '客户号']]

        iv_results = []

        for feature in feature_cols:
            iv_value = self.calculate_iv(self.labeled_data[feature], self.labeled_data[target_col])
            iv_results.append({
                '特征名': feature,
                'IV值': iv_value
            })

        # 转换为DataFrame并排序
        iv_df = pd.DataFrame(iv_results).sort_values('IV值', ascending=False)

        # 筛选IV值大于阈值的特征
        selected_features = iv_df[iv_df['IV值'] > threshold]['特征名'].tolist()

        print(f"IV值阈值：{threshold}")
        print(f"原始特征数量：{len(feature_cols)}")
        print(f"通过IV筛选的特征数量：{len(selected_features)}")

        # 保存IV值结果
        iv_file = os.path.join(self.output_dir, f"{self.timestamp}_iv_values.csv")
        iv_df.to_csv(iv_file, index=False, encoding='utf-8-sig')

        # 显示前10个最重要的特征
        print("\nIV值最高的前10个特征：")
        print(iv_df.head(10))

        return selected_features

    def feature_selection_model_importance(self, features):
        """
        基于模型重要性进行特征选择

        参数说明：
        features (list): 候选特征列表

        返回值：
        dict: 包含不同重要性方法结果的字典
        """
        print("\n" + "="*50)
        print("开始基于模型重要性的特征选择...")

        target_col = '是否购买理财'
        X = self.labeled_data[features]
        y = self.labeled_data[target_col]

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        importance_results = {}

        # 1. LightGBM特征重要性
        print("计算LightGBM特征重要性...")
        train_data = lgb.Dataset(X_train, label=y_train)
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'verbose': -1
        }

        lgb_model = lgb.train(params, train_data, num_boost_round=100)
        lgb_importance = lgb_model.feature_importance(importance_type='gain')

        lgb_importance_df = pd.DataFrame({
            '特征名': features,
            'LGB重要性': lgb_importance
        }).sort_values('LGB重要性', ascending=False)

        importance_results['lgb'] = lgb_importance_df

        # 2. SHAP值
        print("计算SHAP值...")
        explainer = shap.TreeExplainer(lgb_model)
        shap_values = explainer.shap_values(X_test)

        # 计算SHAP重要性（平均绝对SHAP值）
        shap_importance = np.abs(shap_values).mean(axis=0)
        shap_importance_df = pd.DataFrame({
            '特征名': features,
            'SHAP重要性': shap_importance
        }).sort_values('SHAP重要性', ascending=False)

        importance_results['shap'] = shap_importance_df

        # 3. 排列重要性
        print("计算排列重要性...")
        # 创建一个包装器来使LightGBM兼容sklearn的permutation_importance
        class LGBWrapper:
            def __init__(self, lgb_model):
                self.lgb_model = lgb_model

            def fit(self, X, y):
                # 这个方法不会被调用，但需要存在
                return self

            def predict(self, X):
                return (self.lgb_model.predict(X) > 0.5).astype(int)

            def predict_proba(self, X):
                proba = self.lgb_model.predict(X)
                return np.column_stack([1 - proba, proba])

        lgb_wrapper = LGBWrapper(lgb_model)
        perm_importance = permutation_importance(
            lgb_wrapper, X_test, y_test, n_repeats=5, random_state=42, scoring='accuracy'
        )

        perm_importance_df = pd.DataFrame({
            '特征名': features,
            '排列重要性': perm_importance.importances_mean
        }).sort_values('排列重要性', ascending=False)

        importance_results['permutation'] = perm_importance_df

        # 保存重要性结果
        for method, df in importance_results.items():
            file_path = os.path.join(self.output_dir, f"{self.timestamp}_{method}_importance.csv")
            df.to_csv(file_path, index=False, encoding='utf-8-sig')

        # 绘制SHAP summary plot
        plt.figure(figsize=(10, 8))
        shap.summary_plot(shap_values, X_test, feature_names=features, show=False)
        plt.title('SHAP特征重要性汇总图', fontsize=14, fontweight='bold')
        shap_plot_file = os.path.join(self.output_dir, f"{self.timestamp}_shap_summary.png")
        plt.savefig(shap_plot_file, dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 模型重要性计算完成")

        return importance_results

    def select_final_features(self, importance_results, top_n=30):
        """
        确定最终特征集
        综合多种重要性方法的结果

        参数说明：
        importance_results (dict): 重要性结果字典
        top_n (int): 最终选择的特征数量

        返回值：
        list: 最终选择的特征列表
        """
        print("\n" + "="*50)
        print("确定最终特征集...")

        # 获取各方法的top特征
        lgb_top = set(importance_results['lgb'].head(top_n)['特征名'].tolist())
        shap_top = set(importance_results['shap'].head(top_n)['特征名'].tolist())
        perm_top = set(importance_results['permutation'].head(top_n)['特征名'].tolist())

        # 计算特征的综合排名分数
        all_features = set(lgb_top) | set(shap_top) | set(perm_top)
        feature_scores = {}

        for feature in all_features:
            score = 0

            # LightGBM排名分数
            lgb_rank = importance_results['lgb'][
                importance_results['lgb']['特征名'] == feature
            ].index[0] + 1
            score += (top_n - lgb_rank + 1) / top_n

            # SHAP排名分数
            shap_rank = importance_results['shap'][
                importance_results['shap']['特征名'] == feature
            ].index[0] + 1
            score += (top_n - shap_rank + 1) / top_n

            # 排列重要性排名分数
            perm_rank = importance_results['permutation'][
                importance_results['permutation']['特征名'] == feature
            ].index[0] + 1
            score += (top_n - perm_rank + 1) / top_n

            feature_scores[feature] = score

        # 按综合分数排序选择最终特征
        sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
        self.final_features = [feature for feature, score in sorted_features[:top_n]]

        print(f"最终选择{len(self.final_features)}个特征")
        print("最终特征列表（按重要性排序）：")
        for i, (feature, score) in enumerate(sorted_features[:top_n]):
            print(f"{i+1:2d}. {feature} (综合分数: {score:.3f})")

        # 保存最终特征列表
        final_features_file = os.path.join(self.output_dir, "final_features.json")
        with open(final_features_file, 'w', encoding='utf-8') as f:
            json.dump(self.final_features, f, ensure_ascii=False, indent=2)

        # 保存特征选择摘要
        feature_summary = []
        for i, (feature, score) in enumerate(sorted_features[:top_n]):
            feature_summary.append({
                '排名': i + 1,
                '特征名': feature,
                '综合分数': round(score, 3)
            })

        summary_df = pd.DataFrame(feature_summary)
        summary_file = os.path.join(self.output_dir, f"{self.timestamp}_final_features_summary.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')

        print(f"✅ 最终特征列表已保存至：{final_features_file}")

        return self.final_features

    def generate_analysis_report(self):
        """
        生成特征选择分析报告
        """
        print("\n" + "="*50)
        print("生成特征选择分析报告...")

        report_file = os.path.join(self.output_dir, f"{self.timestamp}_feature_selection_report.md")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# PU学习与特征选择分析报告\n\n")
            f.write(f"**生成时间：** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
            f.write("---\n\n")

            # 负样本生成摘要
            if self.labeled_data is not None:
                target_counts = self.labeled_data['是否购买理财'].value_counts()
                f.write("## 1. 负样本生成结果\n\n")
                f.write(f"- **正样本数量：** {target_counts.get(1, 0):,}个\n")
                f.write(f"- **负样本数量：** {target_counts.get(0, 0):,}个\n")
                f.write(f"- **样本比例：** 1:{target_counts.get(0, 0)/target_counts.get(1, 1):.1f}\n\n")

            # 特征选择摘要
            if self.final_features is not None:
                f.write("## 2. 特征选择结果\n\n")
                f.write(f"- **最终特征数量：** {len(self.final_features)}个\n")
                f.write("- **选择方法：** IV值筛选 + 模型重要性 + SHAP值 + 排列重要性\n\n")

                f.write("### 最终选择的特征列表\n\n")
                for i, feature in enumerate(self.final_features):
                    f.write(f"{i+1}. {feature}\n")
                f.write("\n")

            # 方法说明
            f.write("## 3. 方法说明\n\n")
            f.write("### 负样本生成方法\n\n")
            f.write("本项目提供三种负样本生成方法：\n\n")
            f.write("1. **Spy Technique：** 将部分正样本作为'间谍'混入未知样本，训练分类器识别可靠负样本\n")
            f.write("2. **Isolation Forest：** 基于异常检测，将远离正样本分布的未知样本标记为负样本\n")
            f.write("3. **One-Class SVM：** 使用单类支持向量机识别异常样本作为负样本\n\n")

            f.write("### 特征选择流程\n\n")
            f.write("1. **IV值粗筛：** 使用信息价值(Information Value)剔除区分能力弱的特征\n")
            f.write("2. **模型重要性：** 基于LightGBM模型的特征重要性评估\n")
            f.write("3. **SHAP值分析：** 使用SHAP值评估特征的全局和局部贡献度\n")
            f.write("4. **排列重要性：** 通过特征打乱评估对模型性能的影响\n")
            f.write("5. **综合排名：** 融合多种方法的结果，选择最终特征集\n\n")

            # 业务建议
            f.write("## 4. 业务建议\n\n")
            f.write("1. **模型训练：** 使用生成的标注数据进行模型训练\n")
            f.write("2. **特征监控：** 定期监控特征重要性的变化\n")
            f.write("3. **样本更新：** 根据业务反馈更新正负样本标注\n")
            f.write("4. **模型验证：** 使用业务专家知识验证模型结果的合理性\n\n")

            f.write("---\n\n")
            f.write("*本报告由PU学习与特征选择模块自动生成*\n")

        print(f"分析报告已保存至：{report_file}")
        return report_file

    def run_complete_analysis(self, negative_method='spy', iv_threshold=0.02, top_n=30):
        """
        运行完整的PU学习与特征选择流程

        参数说明：
        negative_method (str): 负样本生成方法
        iv_threshold (float): IV值阈值
        top_n (int): 最终选择的特征数量

        返回值：
        bool: 分析是否成功完成
        """
        try:
            print("开始执行完整的PU学习与特征选择流程...")
            print("="*60)

            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 用户选择负样本生成方法
            print(f"\n可选的负样本生成方法：")
            print("1. spy - Spy Technique")
            print("2. isolation - Isolation Forest")
            print("3. ocsvm - One-Class SVM")

            method_choice = input(f"\n请选择负样本生成方法 (默认: {negative_method}): ").strip()
            if method_choice in ['spy', 'isolation', 'ocsvm']:
                negative_method = method_choice

            # 3. 生成负样本
            if not self.generate_negative_samples(method=negative_method):
                return False

            # 4. 正负样本对比分析
            self.analyze_positive_negative_samples()

            # 5. IV值特征筛选
            iv_features = self.feature_selection_iv(threshold=iv_threshold)

            if len(iv_features) == 0:
                print("错误：没有特征通过IV值筛选")
                return False

            # 6. 模型重要性分析
            importance_results = self.feature_selection_model_importance(iv_features)

            # 7. 确定最终特征集
            self.select_final_features(importance_results, top_n=top_n)

            # 8. 生成分析报告
            self.generate_analysis_report()

            print("\n" + "="*60)
            print("✅ PU学习与特征选择完成！")
            print(f"所有结果已保存至：{self.output_dir}")

            return True

        except Exception as e:
            print(f"❌ 分析过程中出现错误：{str(e)}")
            return False


def main():
    """
    主函数：执行PU学习与特征选择

    使用说明：
    1. 确保预处理后的数据文件存在
    2. 根据提示选择负样本生成方法
    3. 查看生成的分析结果和特征列表

    迁移到其他项目时需要修改的部分：
    - data_path: 修改为实际的预处理数据文件路径
    - output_dir: 修改为期望的输出目录
    - 目标变量列名和ID列名
    - IV阈值和最终特征数量根据业务需求调整
    """

    # 配置参数（迁移时需要修改这些路径）
    data_path = "output4/02_processing/processed_data.csv"  # 预处理后的数据文件路径
    output_dir = "output4/03_feature_selection"  # 输出目录

    # 创建PU学习特征选择器实例
    selector = PULearningFeatureSelector(data_path, output_dir)

    # 执行完整分析
    success = selector.run_complete_analysis()

    if success:
        print("\n🎉 PU学习与特征选择成功完成！")
        print("📊 请查看生成的分析结果和特征列表")
        print(f"📁 最终特征列表：{output_dir}/final_features.json")
    else:
        print("\n❌ PU学习与特征选择失败，请检查错误信息")


if __name__ == "__main__":
    main()
