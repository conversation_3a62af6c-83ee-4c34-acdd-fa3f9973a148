# 潜在客户预测分析报告

**生成时间：** 2025年06月08日 10:44:41

---

## 1. 模型概述

### 技术方案

- **不平衡数据处理：** EasyEnsemble方法
- **基分类器：** LightGBM
- **集成策略：** 软投票（概率平均）
- **验证方法：** 5折分层交叉验证

### EasyEnsemble算法技术原理

EasyEnsemble是一种专门处理不平衡数据的集成学习方法，其核心思想是通过多次欠采样和集成学习来解决类别不平衡问题：

#### 算法实现细节

**第一步：多次随机欠采样**
- 保留所有正样本（少数类）
- 对负样本（多数类）进行多次随机欠采样
- 每次采样的负样本数量可根据设定比例调整（1:1到1:5）
- 生成多个平衡的训练子集

**第二步：基分类器训练**
- 在每个平衡子集上训练一个基分类器（LightGBM或AdaBoost）
- 每个基分类器都能在平衡数据上获得较好的性能
- 避免了传统方法中多数类主导的问题

**第三步：集成预测**
- 软投票：对所有基分类器的预测概率进行平均
- 最终预测概率 = Σ(基分类器i的预测概率) / 基分类器数量
- 预测标签 = 1 if 平均概率 > 0.5 else 0

#### 算法优势

1. **保留信息完整性：** 所有正样本都被使用，不丢失重要信息
2. **降低过拟合风险：** 多个模型的集成减少了单一模型的过拟合
3. **提高泛化能力：** 不同子集训练的模型具有多样性
4. **参数可调节：** 可以调整子分类器数量和正负样本比例

#### 技术参数说明

- **子分类器数量：** 默认10个，范围5-20，影响模型复杂度和训练时间
- **正负样本比例：** 默认1:1，可调整为1:2到1:5，影响模型对少数类的敏感度
- **基分类器类型：** LightGBM（推荐）或AdaBoost
- **交叉验证：** 5折分层交叉验证确保评估结果的可靠性

## 2. 特征信息

**使用特征数量：** 19个

**主要特征：**
1. 存款总额年日均
2. 近12月活期交易总金额
3. 存贷款EVA
4. 近12月活期转出金额
5. 近12月活期转入金额
6. 企业网银-本年登录次数
7. 是否达标有效户_encoded
8. 近12月活期交易总笔数
9. 近12月活期转出笔数
10. 当年月均交易笔数
11. 近12月活期转入笔数
12. 持有产品总数
13. 是否活跃客户
14. 是否达标价值户_encoded
15. 是否销户_encoded
16. 代扣税费-是否本年使用
17. 是否代发客户_encoded
18. 是否有贷户_encoded
19. 首个账户开户日_encoded

## 3. 模型性能

### 评估指标说明

- **AUC：** 受试者工作特征曲线下面积，衡量模型区分能力
- **F1 Score：** 精确率和召回率的调和平均，平衡考虑两个指标
- **G-Mean：** 几何平均数，适用于不平衡数据的评估
- **MCC：** 马修斯相关系数，考虑真正例、假正例、真负例、假负例

## 4. 业务应用建议

### 客户分层策略

根据预测概率对客户进行分层管理：

- **高潜力客户 (概率 > 0.7)：** 重点营销，个性化服务
- **中等潜力客户 (0.3 < 概率 ≤ 0.7)：** 定期跟进，培育转化
- **低潜力客户 (概率 ≤ 0.3)：** 基础维护，长期培养

### 营销策略建议

1. **精准营销：** 针对高潜力客户制定专门的营销方案
2. **资源配置：** 根据客户潜力合理分配营销资源
3. **效果监控：** 定期跟踪预测效果，优化模型参数
4. **反馈循环：** 收集营销结果，更新训练数据

## 5. 注意事项

1. **模型更新：** 建议定期使用新数据重新训练模型
2. **特征监控：** 关注特征分布的变化，及时调整
3. **业务验证：** 结合业务专家经验验证预测结果
4. **伦理考量：** 确保模型使用符合相关法规要求

---

*本报告由潜在客户预测模块自动生成*
